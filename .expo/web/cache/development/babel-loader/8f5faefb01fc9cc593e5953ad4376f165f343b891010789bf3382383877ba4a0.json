{"ast": null, "code": "import canUseDOM from \"../canUseDom\";\nvar _requestIdleCallback = function _requestIdleCallback(cb, options) {\n  return setTimeout(function () {\n    var start = Date.now();\n    cb({\n      didTimeout: false,\n      timeRemaining: function timeRemaining() {\n        return Math.max(0, 50 - (Date.now() - start));\n      }\n    });\n  }, 1);\n};\nvar _cancelIdleCallback = function _cancelIdleCallback(id) {\n  clearTimeout(id);\n};\nvar isSupported = canUseDOM && typeof window.requestIdleCallback !== 'undefined';\nvar requestIdleCallback = isSupported ? window.requestIdleCallback : _requestIdleCallback;\nvar cancelIdleCallback = isSupported ? window.cancelIdleCallback : _cancelIdleCallback;\nexport default requestIdleCallback;\nexport { cancelIdleCallback };", "map": {"version": 3, "names": ["canUseDOM", "_requestIdleCallback", "cb", "options", "setTimeout", "start", "Date", "now", "didTimeout", "timeRemaining", "Math", "max", "_cancelIdleCallback", "id", "clearTimeout", "isSupported", "window", "requestIdleCallback", "cancelIdleCallback"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/modules/requestIdleCallback/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nimport canUseDOM from '../canUseDom';\nvar _requestIdleCallback = function _requestIdleCallback(cb, options) {\n  return setTimeout(() => {\n    var start = Date.now();\n    cb({\n      didTimeout: false,\n      timeRemaining() {\n        return Math.max(0, 50 - (Date.now() - start));\n      }\n    });\n  }, 1);\n};\nvar _cancelIdleCallback = function _cancelIdleCallback(id) {\n  clearTimeout(id);\n};\nvar isSupported = canUseDOM && typeof window.requestIdleCallback !== 'undefined';\nvar requestIdleCallback = isSupported ? window.requestIdleCallback : _requestIdleCallback;\nvar cancelIdleCallback = isSupported ? window.cancelIdleCallback : _cancelIdleCallback;\nexport default requestIdleCallback;\nexport { cancelIdleCallback };"], "mappings": "AAQA,OAAOA,SAAS;AAChB,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACpE,OAAOC,UAAU,CAAC,YAAM;IACtB,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACtBL,EAAE,CAAC;MACDM,UAAU,EAAE,KAAK;MACjBC,aAAa,WAAbA,aAAaA,CAAA,EAAG;QACd,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIO,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,EAAE,EAAE;EACzDC,YAAY,CAACD,EAAE,CAAC;AAClB,CAAC;AACD,IAAIE,WAAW,GAAGf,SAAS,IAAI,OAAOgB,MAAM,CAACC,mBAAmB,KAAK,WAAW;AAChF,IAAIA,mBAAmB,GAAGF,WAAW,GAAGC,MAAM,CAACC,mBAAmB,GAAGhB,oBAAoB;AACzF,IAAIiB,kBAAkB,GAAGH,WAAW,GAAGC,MAAM,CAACE,kBAAkB,GAAGN,mBAAmB;AACtF,eAAeK,mBAAmB;AAClC,SAASC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}