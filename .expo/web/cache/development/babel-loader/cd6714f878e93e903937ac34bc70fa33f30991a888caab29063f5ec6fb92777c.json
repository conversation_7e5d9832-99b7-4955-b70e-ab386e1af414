{"ast": null, "code": "var Platform = {\n  OS: 'web',\n  select: function select(obj) {\n    return 'web' in obj ? obj.web : obj.default;\n  },\n  get isTesting() {\n    if (process.env.NODE_ENV === 'test') {\n      return true;\n    }\n    return false;\n  }\n};\nexport default Platform;", "map": {"version": 3, "names": ["Platform", "OS", "select", "obj", "web", "default", "isTesting", "process", "env", "NODE_ENV"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/exports/Platform/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar Platform = {\n  OS: 'web',\n  select: obj => 'web' in obj ? obj.web : obj.default,\n  get isTesting() {\n    if (process.env.NODE_ENV === 'test') {\n      return true;\n    }\n    return false;\n  }\n};\nexport default Platform;"], "mappings": "AAUA,IAAIA,QAAQ,GAAG;EACbC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,SAARA,MAAMA,CAAEC,GAAG;IAAA,OAAI,KAAK,IAAIA,GAAG,GAAGA,GAAG,CAACC,GAAG,GAAGD,GAAG,CAACE,OAAO;EAAA;EACnD,IAAIC,SAASA,CAAA,EAAG;IACd,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;AACF,CAAC;AACD,eAAeT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}