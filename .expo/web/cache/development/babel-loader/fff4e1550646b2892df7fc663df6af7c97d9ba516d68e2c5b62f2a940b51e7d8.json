{"ast": null, "code": "var assets = [];\nexport function registerAsset(asset) {\n  return assets.push(asset);\n}\nexport function getAssetByID(assetId) {\n  return assets[assetId - 1];\n}", "map": {"version": 3, "names": ["assets", "registerAsset", "asset", "push", "getAssetByID", "assetId"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/modules/AssetRegistry/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar assets = [];\nexport function registerAsset(asset) {\n  // `push` returns new array length, so the first asset will\n  // get id 1 (not 0) to make the value truthy\n  return assets.push(asset);\n}\nexport function getAssetByID(assetId) {\n  return assets[assetId - 1];\n}"], "mappings": "AASA,IAAIA,MAAM,GAAG,EAAE;AACf,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EAGnC,OAAOF,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC;AAC3B;AACA,OAAO,SAASE,YAAYA,CAACC,OAAO,EAAE;EACpC,OAAOL,MAAM,CAACK,OAAO,GAAG,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}