{"ast": null, "code": "import isWebColor from \"../../../modules/isWebColor\";\nimport processColor from \"../../../exports/processColor\";\nvar normalizeColor = function normalizeColor(color, opacity) {\n  if (opacity === void 0) {\n    opacity = 1;\n  }\n  if (color == null) return;\n  if (typeof color === 'string' && isWebColor(color)) {\n    return color;\n  }\n  var colorInt = processColor(color);\n  if (colorInt != null) {\n    var r = colorInt >> 16 & 255;\n    var g = colorInt >> 8 & 255;\n    var b = colorInt & 255;\n    var a = (colorInt >> 24 & 255) / 255;\n    var alpha = (a * opacity).toFixed(2);\n    return \"rgba(\" + r + \",\" + g + \",\" + b + \",\" + alpha + \")\";\n  }\n};\nexport default normalizeColor;", "map": {"version": 3, "names": ["isWebColor", "processColor", "normalizeColor", "color", "opacity", "colorInt", "r", "g", "b", "a", "alpha", "toFixed"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/exports/StyleSheet/compiler/normalizeColor.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport isWebColor from '../../../modules/isWebColor';\nimport processColor from '../../../exports/processColor';\nvar normalizeColor = function normalizeColor(color, opacity) {\n  if (opacity === void 0) {\n    opacity = 1;\n  }\n  if (color == null) return;\n  if (typeof color === 'string' && isWebColor(color)) {\n    return color;\n  }\n  var colorInt = processColor(color);\n  if (colorInt != null) {\n    var r = colorInt >> 16 & 255;\n    var g = colorInt >> 8 & 255;\n    var b = colorInt & 255;\n    var a = (colorInt >> 24 & 255) / 255;\n    var alpha = (a * opacity).toFixed(2);\n    return \"rgba(\" + r + \",\" + g + \",\" + b + \",\" + alpha + \")\";\n  }\n};\nexport default normalizeColor;"], "mappings": "AASA,OAAOA,UAAU;AACjB,OAAOC,YAAY;AACnB,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC3D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EACA,IAAID,KAAK,IAAI,IAAI,EAAE;EACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIH,UAAU,CAACG,KAAK,CAAC,EAAE;IAClD,OAAOA,KAAK;EACd;EACA,IAAIE,QAAQ,GAAGJ,YAAY,CAACE,KAAK,CAAC;EAClC,IAAIE,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAIC,CAAC,GAAGD,QAAQ,IAAI,EAAE,GAAG,GAAG;IAC5B,IAAIE,CAAC,GAAGF,QAAQ,IAAI,CAAC,GAAG,GAAG;IAC3B,IAAIG,CAAC,GAAGH,QAAQ,GAAG,GAAG;IACtB,IAAII,CAAC,GAAG,CAACJ,QAAQ,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG;IACpC,IAAIK,KAAK,GAAG,CAACD,CAAC,GAAGL,OAAO,EAAEO,OAAO,CAAC,CAAC,CAAC;IACpC,OAAO,OAAO,GAAGL,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG;EAC5D;AACF,CAAC;AACD,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}