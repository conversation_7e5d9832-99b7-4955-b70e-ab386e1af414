{"ast": null, "code": "'use strict';\n\nvar ReactNativeFeatureFlags = {\n  isLayoutAnimationEnabled: function isLayoutAnimationEnabled() {\n    return true;\n  },\n  shouldEmitW3CPointerEvents: function shouldEmitW3CPointerEvents() {\n    return false;\n  },\n  shouldPressibilityUseW3CPointerEventsForHover: function shouldPressibilityUseW3CPointerEventsForHover() {\n    return false;\n  },\n  animatedShouldDebounceQueueFlush: function animatedShouldDebounceQueueFlush() {\n    return false;\n  },\n  animatedShouldUseSingleOp: function animatedShouldUseSingleOp() {\n    return false;\n  }\n};\nexport default ReactNativeFeatureFlags;", "map": {"version": 3, "names": ["ReactNativeFeatureFlags", "isLayoutAnimationEnabled", "shouldEmitW3CPointerEvents", "shouldPressibilityUseW3CPointerEventsForHover", "animatedShouldDebounceQueueFlush", "animatedShouldUseSingleOp"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/ReactNative/ReactNativeFeatureFlags.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar ReactNativeFeatureFlags = {\n  isLayoutAnimationEnabled: () => true,\n  shouldEmitW3CPointerEvents: () => false,\n  shouldPressibilityUseW3CPointerEventsForHover: () => false,\n  animatedShouldDebounceQueueFlush: () => false,\n  animatedShouldUseSingleOp: () => false\n};\nexport default ReactNativeFeatureFlags;"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAG;EAC5BC,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAA;IAAA,OAAQ,IAAI;EAAA;EACpCC,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAA;IAAA,OAAQ,KAAK;EAAA;EACvCC,6CAA6C,EAAE,SAA/CA,6CAA6CA,CAAA;IAAA,OAAQ,KAAK;EAAA;EAC1DC,gCAAgC,EAAE,SAAlCA,gCAAgCA,CAAA;IAAA,OAAQ,KAAK;EAAA;EAC7CC,yBAAyB,EAAE,SAA3BA,yBAAyBA,CAAA;IAAA,OAAQ,KAAK;EAAA;AACxC,CAAC;AACD,eAAeL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}