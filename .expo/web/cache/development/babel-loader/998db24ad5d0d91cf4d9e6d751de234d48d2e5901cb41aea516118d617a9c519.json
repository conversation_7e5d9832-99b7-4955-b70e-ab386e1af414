{"ast": null, "code": "'use strict';\nfunction infoLog() {\n  var _console;\n  return (_console = console).log.apply(_console, arguments);\n}\nexport default infoLog;", "map": {"version": 3, "names": ["infoLog", "_console", "console", "log", "apply", "arguments"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/infoLog/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\n/**\n * Intentional info-level logging for clear separation from ad-hoc console debug logging.\n */\nfunction infoLog() {\n  return console.log(...arguments);\n}\nexport default infoLog;"], "mappings": "AAUA,YAAY;AAKZ,SAASA,OAAOA,CAAA,EAAG;EAAA,IAAAC,QAAA;EACjB,OAAO,CAAAA,QAAA,GAAAC,OAAO,EAACC,GAAG,CAAAC,KAAA,CAAAH,QAAA,EAAII,SAAS,CAAC;AAClC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}