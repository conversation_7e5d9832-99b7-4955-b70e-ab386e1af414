{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AnimatedValue from \"./AnimatedValue\";\nimport AnimatedNode from \"./AnimatedNode\";\nimport { generateNewAnimationId, shouldUseNativeDriver } from \"../NativeAnimatedHelper\";\nvar AnimatedTracking = function (_AnimatedNode) {\n  function AnimatedTracking(value, parent, animationClass, animationConfig, callback) {\n    var _this;\n    _classCallCheck(this, AnimatedTracking);\n    _this = _callSuper(this, AnimatedTracking);\n    _this._value = value;\n    _this._parent = parent;\n    _this._animationClass = animationClass;\n    _this._animationConfig = animationConfig;\n    _this._useNativeDriver = shouldUseNativeDriver(animationConfig);\n    _this._callback = callback;\n    _this.__attach();\n    return _this;\n  }\n  _inherits(AnimatedTracking, _AnimatedNode);\n  return _createClass(AnimatedTracking, [{\n    key: \"__makeNative\",\n    value: function __makeNative() {\n      this.__isNative = true;\n      this._parent.__makeNative();\n      _superPropGet(AnimatedTracking, \"__makeNative\", this, 3)([]);\n      this._value.__makeNative();\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return this._parent.__getValue();\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      this._parent.__addChild(this);\n      if (this._useNativeDriver) {\n        this.__makeNative();\n      }\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      this._parent.__removeChild(this);\n      _superPropGet(AnimatedTracking, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this._value.animate(new this._animationClass(_objectSpread(_objectSpread({}, this._animationConfig), {}, {\n        toValue: this._animationConfig.toValue.__getValue()\n      })), this._callback);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      var animation = new this._animationClass(_objectSpread(_objectSpread({}, this._animationConfig), {}, {\n        toValue: undefined\n      }));\n      var animationConfig = animation.__getNativeAnimationConfig();\n      return {\n        type: 'tracking',\n        animationId: generateNewAnimationId(),\n        animationConfig: animationConfig,\n        toValue: this._parent.__getNativeTag(),\n        value: this._value.__getNativeTag()\n      };\n    }\n  }]);\n}(AnimatedNode);\nexport default AnimatedTracking;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_objectSpread", "AnimatedValue", "AnimatedNode", "generateNewAnimationId", "shouldUseNativeDriver", "AnimatedTracking", "_AnimatedNode", "value", "parent", "animationClass", "animationConfig", "callback", "_this", "_value", "_parent", "_animationClass", "_animationConfig", "_useNativeDriver", "_callback", "__attach", "key", "__makeNative", "__isNative", "__getValue", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "update", "animate", "toValue", "__getNativeConfig", "animation", "undefined", "__getNativeAnimationConfig", "type", "animationId", "__getNativeTag"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedTracking.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedNode from './AnimatedNode';\nimport { generateNewAnimationId, shouldUseNativeDriver } from '../NativeAnimatedHelper';\nclass AnimatedTracking extends AnimatedNode {\n  constructor(value, parent, animationClass, animationConfig, callback) {\n    super();\n    this._value = value;\n    this._parent = parent;\n    this._animationClass = animationClass;\n    this._animationConfig = animationConfig;\n    this._useNativeDriver = shouldUseNativeDriver(animationConfig);\n    this._callback = callback;\n    this.__attach();\n  }\n  __makeNative() {\n    this.__isNative = true;\n    this._parent.__makeNative();\n    super.__makeNative();\n    this._value.__makeNative();\n  }\n  __getValue() {\n    return this._parent.__getValue();\n  }\n  __attach() {\n    this._parent.__addChild(this);\n    if (this._useNativeDriver) {\n      // when the tracking starts we need to convert this node to a \"native node\"\n      // so that the parent node will be made \"native\" too. This is necessary as\n      // if we don't do this `update` method will get called. At that point it\n      // may be too late as it would mean the JS driver has already started\n      // updating node values\n      this.__makeNative();\n    }\n  }\n  __detach() {\n    this._parent.__removeChild(this);\n    super.__detach();\n  }\n  update() {\n    this._value.animate(new this._animationClass(_objectSpread(_objectSpread({}, this._animationConfig), {}, {\n      toValue: this._animationConfig.toValue.__getValue()\n    })), this._callback);\n  }\n  __getNativeConfig() {\n    var animation = new this._animationClass(_objectSpread(_objectSpread({}, this._animationConfig), {}, {\n      // remove toValue from the config as it's a ref to Animated.Value\n      toValue: undefined\n    }));\n    var animationConfig = animation.__getNativeAnimationConfig();\n    return {\n      type: 'tracking',\n      animationId: generateNewAnimationId(),\n      animationConfig,\n      toValue: this._parent.__getNativeTag(),\n      value: this._value.__getNativeTag()\n    };\n  }\n}\nexport default AnimatedTracking;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,aAAa;AACpB,OAAOC,YAAY;AACnB,SAASC,sBAAsB,EAAEC,qBAAqB;AAAkC,IAClFC,gBAAgB,aAAAC,aAAA;EACpB,SAAAD,iBAAYE,KAAK,EAAEC,MAAM,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAAlC,eAAA,OAAA2B,gBAAA;IACpEO,KAAA,GAAA5B,UAAA,OAAAqB,gBAAA;IACAO,KAAA,CAAKC,MAAM,GAAGN,KAAK;IACnBK,KAAA,CAAKE,OAAO,GAAGN,MAAM;IACrBI,KAAA,CAAKG,eAAe,GAAGN,cAAc;IACrCG,KAAA,CAAKI,gBAAgB,GAAGN,eAAe;IACvCE,KAAA,CAAKK,gBAAgB,GAAGb,qBAAqB,CAACM,eAAe,CAAC;IAC9DE,KAAA,CAAKM,SAAS,GAAGP,QAAQ;IACzBC,KAAA,CAAKO,QAAQ,CAAC,CAAC;IAAC,OAAAP,KAAA;EAClB;EAAC7B,SAAA,CAAAsB,gBAAA,EAAAC,aAAA;EAAA,OAAA3B,YAAA,CAAA0B,gBAAA;IAAAe,GAAA;IAAAb,KAAA,EACD,SAAAc,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACR,OAAO,CAACO,YAAY,CAAC,CAAC;MAC3BxB,aAAA,CAAAQ,gBAAA;MACA,IAAI,CAACQ,MAAM,CAACQ,YAAY,CAAC,CAAC;IAC5B;EAAC;IAAAD,GAAA;IAAAb,KAAA,EACD,SAAAgB,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACT,OAAO,CAACS,UAAU,CAAC,CAAC;IAClC;EAAC;IAAAH,GAAA;IAAAb,KAAA,EACD,SAAAY,QAAQA,CAAA,EAAG;MACT,IAAI,CAACL,OAAO,CAACU,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,IAAI,CAACP,gBAAgB,EAAE;QAMzB,IAAI,CAACI,YAAY,CAAC,CAAC;MACrB;IACF;EAAC;IAAAD,GAAA;IAAAb,KAAA,EACD,SAAAkB,QAAQA,CAAA,EAAG;MACT,IAAI,CAACX,OAAO,CAACY,aAAa,CAAC,IAAI,CAAC;MAChC7B,aAAA,CAAAQ,gBAAA;IACF;EAAC;IAAAe,GAAA;IAAAb,KAAA,EACD,SAAAoB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACd,MAAM,CAACe,OAAO,CAAC,IAAI,IAAI,CAACb,eAAe,CAACf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QACvGa,OAAO,EAAE,IAAI,CAACb,gBAAgB,CAACa,OAAO,CAACN,UAAU,CAAC;MACpD,CAAC,CAAC,CAAC,EAAE,IAAI,CAACL,SAAS,CAAC;IACtB;EAAC;IAAAE,GAAA;IAAAb,KAAA,EACD,SAAAuB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,SAAS,GAAG,IAAI,IAAI,CAAChB,eAAe,CAACf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAEnGa,OAAO,EAAEG;MACX,CAAC,CAAC,CAAC;MACH,IAAItB,eAAe,GAAGqB,SAAS,CAACE,0BAA0B,CAAC,CAAC;MAC5D,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAEhC,sBAAsB,CAAC,CAAC;QACrCO,eAAe,EAAfA,eAAe;QACfmB,OAAO,EAAE,IAAI,CAACf,OAAO,CAACsB,cAAc,CAAC,CAAC;QACtC7B,KAAK,EAAE,IAAI,CAACM,MAAM,CAACuB,cAAc,CAAC;MACpC,CAAC;IACH;EAAC;AAAA,EArD4BlC,YAAY;AAuD3C,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}