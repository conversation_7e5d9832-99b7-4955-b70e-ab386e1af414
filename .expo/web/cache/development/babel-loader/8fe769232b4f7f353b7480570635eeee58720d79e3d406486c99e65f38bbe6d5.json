{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\nimport useAnimatedProps from \"./useAnimatedProps\";\nimport useMergeRefs from \"../Utilities/useMergeRefs\";\nimport StyleSheet from \"../../../exports/StyleSheet\";\nimport View from \"../../../exports/View\";\nimport * as React from 'react';\nexport default function createAnimatedComponent(Component) {\n  return React.forwardRef(function (props, forwardedRef) {\n    var _useAnimatedProps = useAnimatedProps(props),\n      reducedProps = _useAnimatedProps[0],\n      callbackRef = _useAnimatedProps[1];\n    var ref = useMergeRefs(callbackRef, forwardedRef);\n    var passthroughAnimatedPropExplicitValues = reducedProps.passthroughAnimatedPropExplicitValues,\n      style = reducedProps.style;\n    var _ref = passthroughAnimatedPropExplicitValues !== null && passthroughAnimatedPropExplicitValues !== void 0 ? passthroughAnimatedPropExplicitValues : {},\n      passthroughStyle = _ref.style,\n      passthroughProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    var mergedStyle = [style, passthroughStyle];\n    return React.createElement(Component, _extends({}, reducedProps, passthroughProps, {\n      style: mergedStyle,\n      ref: ref\n    }));\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "useAnimatedProps", "useMergeRefs", "StyleSheet", "View", "React", "createAnimatedComponent", "Component", "forwardRef", "props", "forwardedRef", "_useAnimatedProps", "reducedProps", "callback<PERSON><PERSON>", "ref", "passthroughAnimatedPropExplicitValues", "style", "_ref", "passthroughStyle", "passthroughProps", "mergedStyle", "createElement"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/Animated/createAnimatedComponent.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport useAnimatedProps from './useAnimatedProps';\nimport useMergeRefs from '../Utilities/useMergeRefs';\nimport StyleSheet from '../../../exports/StyleSheet';\nimport View from '../../../exports/View';\nimport * as React from 'react';\n/**\n * Experimental implementation of `createAnimatedComponent` that is intended to\n * be compatible with concurrent rendering.\n */\nexport default function createAnimatedComponent(Component) {\n  return /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n    var _useAnimatedProps = useAnimatedProps(props),\n      reducedProps = _useAnimatedProps[0],\n      callbackRef = _useAnimatedProps[1];\n    var ref = useMergeRefs(callbackRef, forwardedRef);\n\n    // Some components require explicit passthrough values for animation\n    // to work properly. For example, if an animated component is\n    // transformed and Pressable, onPress will not work after transform\n    // without these passthrough values.\n    // $FlowFixMe[prop-missing]\n    var passthroughAnimatedPropExplicitValues = reducedProps.passthroughAnimatedPropExplicitValues,\n      style = reducedProps.style;\n    var _ref = passthroughAnimatedPropExplicitValues !== null && passthroughAnimatedPropExplicitValues !== void 0 ? passthroughAnimatedPropExplicitValues : {},\n      passthroughStyle = _ref.style,\n      passthroughProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n    var mergedStyle = [style, passthroughStyle];\n    return /*#__PURE__*/React.createElement(Component, _extends({}, reducedProps, passthroughProps, {\n      style: mergedStyle,\n      ref: ref\n    }));\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,OAAO,CAAC;AAWzB,OAAOC,gBAAgB;AACvB,OAAOC,YAAY;AACnB,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,OAAO,KAAKC,KAAK,MAAM,OAAO;AAK9B,eAAe,SAASC,uBAAuBA,CAACC,SAAS,EAAE;EACzD,OAAoBF,KAAK,CAACG,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;IAC5D,IAAIC,iBAAiB,GAAGV,gBAAgB,CAACQ,KAAK,CAAC;MAC7CG,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACnCE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpC,IAAIG,GAAG,GAAGZ,YAAY,CAACW,WAAW,EAAEH,YAAY,CAAC;IAOjD,IAAIK,qCAAqC,GAAGH,YAAY,CAACG,qCAAqC;MAC5FC,KAAK,GAAGJ,YAAY,CAACI,KAAK;IAC5B,IAAIC,IAAI,GAAGF,qCAAqC,KAAK,IAAI,IAAIA,qCAAqC,KAAK,KAAK,CAAC,GAAGA,qCAAqC,GAAG,CAAC,CAAC;MACxJG,gBAAgB,GAAGD,IAAI,CAACD,KAAK;MAC7BG,gBAAgB,GAAGpB,6BAA6B,CAACkB,IAAI,EAAEjB,SAAS,CAAC;IACnE,IAAIoB,WAAW,GAAG,CAACJ,KAAK,EAAEE,gBAAgB,CAAC;IAC3C,OAAoBb,KAAK,CAACgB,aAAa,CAACd,SAAS,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEc,YAAY,EAAEO,gBAAgB,EAAE;MAC9FH,KAAK,EAAEI,WAAW;MAClBN,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}