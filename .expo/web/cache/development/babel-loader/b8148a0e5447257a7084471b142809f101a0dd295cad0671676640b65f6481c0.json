{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport * as React from 'react';\nimport FlatList from \"../../../../exports/FlatList\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nvar FlatListWithEventThrottle = React.forwardRef(function (props, ref) {\n  return React.createElement(FlatList, _extends({\n    scrollEventThrottle: 0.0001\n  }, props, {\n    ref: ref\n  }));\n});\nexport default createAnimatedComponent(FlatListWithEventThrottle);", "map": {"version": 3, "names": ["_extends", "React", "FlatList", "createAnimatedComponent", "FlatListWithEventThrottle", "forwardRef", "props", "ref", "createElement", "scrollEventThrottle"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedFlatList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport FlatList from '../../../../exports/FlatList';\nimport createAnimatedComponent from '../createAnimatedComponent';\n/**\n * @see https://github.com/facebook/react-native/commit/b8c8562\n */\nvar FlatListWithEventThrottle = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(FlatList, _extends({\n  scrollEventThrottle: 0.0001\n}, props, {\n  ref: ref\n})));\nexport default createAnimatedComponent(FlatListWithEventThrottle);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AAWrD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ;AACf,OAAOC,uBAAuB;AAI9B,IAAIC,yBAAyB,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG;EAAA,OAAkBN,KAAK,CAACO,aAAa,CAACN,QAAQ,EAAEF,QAAQ,CAAC;IAChIS,mBAAmB,EAAE;EACvB,CAAC,EAAEH,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AAAA,EAAC;AACJ,eAAeJ,uBAAuB,CAACC,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}