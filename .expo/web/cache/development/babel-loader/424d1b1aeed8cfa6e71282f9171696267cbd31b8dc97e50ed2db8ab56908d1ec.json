{"ast": null, "code": "'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent, attachNativeEvent } from \"./AnimatedEvent\";\nimport AnimatedAddition from \"./nodes/AnimatedAddition\";\nimport AnimatedDiffClamp from \"./nodes/AnimatedDiffClamp\";\nimport AnimatedDivision from \"./nodes/AnimatedDivision\";\nimport AnimatedInterpolation from \"./nodes/AnimatedInterpolation\";\nimport AnimatedModulo from \"./nodes/AnimatedModulo\";\nimport AnimatedMultiplication from \"./nodes/AnimatedMultiplication\";\nimport AnimatedNode from \"./nodes/AnimatedNode\";\nimport AnimatedProps from \"./nodes/AnimatedProps\";\nimport AnimatedSubtraction from \"./nodes/AnimatedSubtraction\";\nimport AnimatedTracking from \"./nodes/AnimatedTracking\";\nimport AnimatedValue from \"./nodes/AnimatedValue\";\nimport AnimatedValueXY from \"./nodes/AnimatedValueXY\";\nimport DecayAnimation from \"./animations/DecayAnimation\";\nimport SpringAnimation from \"./animations/SpringAnimation\";\nimport TimingAnimation from \"./animations/TimingAnimation\";\nimport createAnimatedComponent from \"./createAnimatedComponent\";\nimport AnimatedColor from \"./nodes/AnimatedColor\";\nvar add = function add(a, b) {\n  return new AnimatedAddition(a, b);\n};\nvar subtract = function subtract(a, b) {\n  return new AnimatedSubtraction(a, b);\n};\nvar divide = function divide(a, b) {\n  return new AnimatedDivision(a, b);\n};\nvar multiply = function multiply(a, b) {\n  return new AnimatedMultiplication(a, b);\n};\nvar modulo = function modulo(a, modulus) {\n  return new AnimatedModulo(a, modulus);\n};\nvar diffClamp = function diffClamp(a, min, max) {\n  return new AnimatedDiffClamp(a, min, max);\n};\nvar _combineCallbacks = function _combineCallbacks(callback, config) {\n  if (callback && config.onComplete) {\n    return function () {\n      config.onComplete && config.onComplete.apply(config, arguments);\n      callback && callback.apply(void 0, arguments);\n    };\n  } else {\n    return callback || config.onComplete;\n  }\n};\nvar maybeVectorAnim = function maybeVectorAnim(value, config, anim) {\n  if (value instanceof AnimatedValueXY) {\n    var configX = _objectSpread({}, config);\n    var configY = _objectSpread({}, config);\n    for (var key in config) {\n      var _config$key = config[key],\n        x = _config$key.x,\n        y = _config$key.y;\n      if (x !== undefined && y !== undefined) {\n        configX[key] = x;\n        configY[key] = y;\n      }\n    }\n    var aX = anim(value.x, configX);\n    var aY = anim(value.y, configY);\n    return parallel([aX, aY], {\n      stopTogether: false\n    });\n  } else if (value instanceof AnimatedColor) {\n    var configR = _objectSpread({}, config);\n    var configG = _objectSpread({}, config);\n    var configB = _objectSpread({}, config);\n    var configA = _objectSpread({}, config);\n    for (var _key in config) {\n      var _config$_key = config[_key],\n        r = _config$_key.r,\n        g = _config$_key.g,\n        b = _config$_key.b,\n        a = _config$_key.a;\n      if (r !== undefined && g !== undefined && b !== undefined && a !== undefined) {\n        configR[_key] = r;\n        configG[_key] = g;\n        configB[_key] = b;\n        configA[_key] = a;\n      }\n    }\n    var aR = anim(value.r, configR);\n    var aG = anim(value.g, configG);\n    var aB = anim(value.b, configB);\n    var aA = anim(value.a, configA);\n    return parallel([aR, aG, aB, aA], {\n      stopTogether: false\n    });\n  }\n  return null;\n};\nvar spring = function spring(value, config) {\n  var _start = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(new AnimatedTracking(singleValue, configuration.toValue, SpringAnimation, singleConfig, callback));\n    } else {\n      singleValue.animate(new SpringAnimation(singleConfig), callback);\n    }\n  };\n  return maybeVectorAnim(value, config, spring) || {\n    start: function start(callback) {\n      _start(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations: iterations\n      });\n      _start(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar timing = function timing(value, config) {\n  var _start2 = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(new AnimatedTracking(singleValue, configuration.toValue, TimingAnimation, singleConfig, callback));\n    } else {\n      singleValue.animate(new TimingAnimation(singleConfig), callback);\n    }\n  };\n  return maybeVectorAnim(value, config, timing) || {\n    start: function start(callback) {\n      _start2(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations: iterations\n      });\n      _start2(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar decay = function decay(value, config) {\n  var _start3 = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    singleValue.animate(new DecayAnimation(singleConfig), callback);\n  };\n  return maybeVectorAnim(value, config, decay) || {\n    start: function start(callback) {\n      _start3(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations: iterations\n      });\n      _start3(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar sequence = function sequence(animations) {\n  var current = 0;\n  return {\n    start: function start(callback) {\n      var onComplete = function onComplete(result) {\n        if (!result.finished) {\n          callback && callback(result);\n          return;\n        }\n        current++;\n        if (current === animations.length) {\n          callback && callback(result);\n          return;\n        }\n        animations[current].start(onComplete);\n      };\n      if (animations.length === 0) {\n        callback && callback({\n          finished: true\n        });\n      } else {\n        animations[current].start(onComplete);\n      }\n    },\n    stop: function stop() {\n      if (current < animations.length) {\n        animations[current].stop();\n      }\n    },\n    reset: function reset() {\n      animations.forEach(function (animation, idx) {\n        if (idx <= current) {\n          animation.reset();\n        }\n      });\n      current = 0;\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.sequence animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return false;\n    }\n  };\n};\nvar parallel = function parallel(animations, config) {\n  var doneCount = 0;\n  var hasEnded = {};\n  var stopTogether = !(config && config.stopTogether === false);\n  var result = {\n    start: function start(callback) {\n      if (doneCount === animations.length) {\n        callback && callback({\n          finished: true\n        });\n        return;\n      }\n      animations.forEach(function (animation, idx) {\n        var cb = function cb(endResult) {\n          hasEnded[idx] = true;\n          doneCount++;\n          if (doneCount === animations.length) {\n            doneCount = 0;\n            callback && callback(endResult);\n            return;\n          }\n          if (!endResult.finished && stopTogether) {\n            result.stop();\n          }\n        };\n        if (!animation) {\n          cb({\n            finished: true\n          });\n        } else {\n          animation.start(cb);\n        }\n      });\n    },\n    stop: function stop() {\n      animations.forEach(function (animation, idx) {\n        !hasEnded[idx] && animation.stop();\n        hasEnded[idx] = true;\n      });\n    },\n    reset: function reset() {\n      animations.forEach(function (animation, idx) {\n        animation.reset();\n        hasEnded[idx] = false;\n        doneCount = 0;\n      });\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.parallel animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return false;\n    }\n  };\n  return result;\n};\nvar delay = function delay(time) {\n  return timing(new AnimatedValue(0), {\n    toValue: 0,\n    delay: time,\n    duration: 0,\n    useNativeDriver: false\n  });\n};\nvar stagger = function stagger(time, animations) {\n  return parallel(animations.map(function (animation, i) {\n    return sequence([delay(time * i), animation]);\n  }));\n};\nvar loop = function loop(animation, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$iterations = _ref.iterations,\n    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations,\n    _ref$resetBeforeItera = _ref.resetBeforeIteration,\n    resetBeforeIteration = _ref$resetBeforeItera === void 0 ? true : _ref$resetBeforeItera;\n  var isFinished = false;\n  var iterationsSoFar = 0;\n  return {\n    start: function start(callback) {\n      var restart = function restart(result) {\n        if (result === void 0) {\n          result = {\n            finished: true\n          };\n        }\n        if (isFinished || iterationsSoFar === iterations || result.finished === false) {\n          callback && callback(result);\n        } else {\n          iterationsSoFar++;\n          resetBeforeIteration && animation.reset();\n          animation.start(restart);\n        }\n      };\n      if (!animation || iterations === 0) {\n        callback && callback({\n          finished: true\n        });\n      } else {\n        if (animation._isUsingNativeDriver()) {\n          animation._startNativeLoop(iterations);\n        } else {\n          restart();\n        }\n      }\n    },\n    stop: function stop() {\n      isFinished = true;\n      animation.stop();\n    },\n    reset: function reset() {\n      iterationsSoFar = 0;\n      isFinished = false;\n      animation.reset();\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.loop animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return animation._isUsingNativeDriver();\n    }\n  };\n};\nfunction forkEvent(event, listener) {\n  if (!event) {\n    return listener;\n  } else if (event instanceof AnimatedEvent) {\n    event.__addListener(listener);\n    return event;\n  } else {\n    return function () {\n      typeof event === 'function' && event.apply(void 0, arguments);\n      listener.apply(void 0, arguments);\n    };\n  }\n}\nfunction unforkEvent(event, listener) {\n  if (event && event instanceof AnimatedEvent) {\n    event.__removeListener(listener);\n  }\n}\nvar event = function event(argMapping, config) {\n  var animatedEvent = new AnimatedEvent(argMapping, config);\n  if (animatedEvent.__isNative) {\n    return animatedEvent;\n  } else {\n    return animatedEvent.__getHandler();\n  }\n};\nexport default {\n  Value: AnimatedValue,\n  ValueXY: AnimatedValueXY,\n  Color: AnimatedColor,\n  Interpolation: AnimatedInterpolation,\n  Node: AnimatedNode,\n  decay: decay,\n  timing: timing,\n  spring: spring,\n  add: add,\n  subtract: subtract,\n  divide: divide,\n  multiply: multiply,\n  modulo: modulo,\n  diffClamp: diffClamp,\n  delay: delay,\n  sequence: sequence,\n  parallel: parallel,\n  stagger: stagger,\n  loop: loop,\n  event: event,\n  createAnimatedComponent: createAnimatedComponent,\n  attachNativeEvent: attachNativeEvent,\n  forkEvent: forkEvent,\n  unforkEvent: unforkEvent,\n  Event: AnimatedEvent\n};", "map": {"version": 3, "names": ["_objectSpread", "AnimatedEvent", "attachNativeEvent", "AnimatedAddition", "AnimatedDiffClamp", "AnimatedDivision", "AnimatedInterpolation", "AnimatedModulo", "AnimatedMultiplication", "AnimatedNode", "AnimatedProps", "AnimatedSubtraction", "AnimatedTracking", "AnimatedValue", "AnimatedValueXY", "DecayAnimation", "SpringAnimation", "TimingAnimation", "createAnimatedComponent", "AnimatedColor", "add", "a", "b", "subtract", "divide", "multiply", "modulo", "modulus", "diffClamp", "min", "max", "_combineCallbacks", "callback", "config", "onComplete", "apply", "arguments", "maybeVectorAnim", "value", "anim", "configX", "configY", "key", "_config$key", "x", "y", "undefined", "aX", "aY", "parallel", "stopTogether", "configR", "configG", "configB", "configA", "_key", "_config$_key", "r", "g", "aR", "aG", "aB", "aA", "spring", "_start", "start", "animatedValue", "configuration", "singleValue", "singleConfig", "stopTracking", "toValue", "track", "animate", "stop", "stopAnimation", "reset", "resetAnimation", "_startNativeLoop", "iterations", "_isUsingNativeDriver", "useNativeDriver", "timing", "_start2", "decay", "_start3", "sequence", "animations", "current", "result", "finished", "length", "for<PERSON>ach", "animation", "idx", "Error", "doneCount", "hasEnded", "cb", "endResult", "delay", "time", "duration", "stagger", "map", "i", "loop", "_temp", "_ref", "_ref$iterations", "_ref$resetBeforeItera", "resetBeforeIteration", "isFinished", "iterationsSoFar", "restart", "forkEvent", "event", "listener", "__addListener", "unforkEvent", "__removeListener", "arg<PERSON><PERSON><PERSON>", "animatedEvent", "__isNative", "__<PERSON><PERSON><PERSON><PERSON>", "Value", "ValueXY", "Color", "Interpolation", "Node", "Event"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/vendor/react-native/Animated/AnimatedImplementation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent, attachNativeEvent } from './AnimatedEvent';\nimport AnimatedAddition from './nodes/AnimatedAddition';\nimport AnimatedDiffClamp from './nodes/AnimatedDiffClamp';\nimport AnimatedDivision from './nodes/AnimatedDivision';\nimport AnimatedInterpolation from './nodes/AnimatedInterpolation';\nimport AnimatedModulo from './nodes/AnimatedModulo';\nimport AnimatedMultiplication from './nodes/AnimatedMultiplication';\nimport AnimatedNode from './nodes/AnimatedNode';\nimport AnimatedProps from './nodes/AnimatedProps';\nimport AnimatedSubtraction from './nodes/AnimatedSubtraction';\nimport AnimatedTracking from './nodes/AnimatedTracking';\nimport AnimatedValue from './nodes/AnimatedValue';\nimport AnimatedValueXY from './nodes/AnimatedValueXY';\nimport DecayAnimation from './animations/DecayAnimation';\nimport SpringAnimation from './animations/SpringAnimation';\nimport TimingAnimation from './animations/TimingAnimation';\nimport createAnimatedComponent from './createAnimatedComponent';\nimport AnimatedColor from './nodes/AnimatedColor';\nvar add = function add(a, b) {\n  return new AnimatedAddition(a, b);\n};\nvar subtract = function subtract(a, b) {\n  return new AnimatedSubtraction(a, b);\n};\nvar divide = function divide(a, b) {\n  return new AnimatedDivision(a, b);\n};\nvar multiply = function multiply(a, b) {\n  return new AnimatedMultiplication(a, b);\n};\nvar modulo = function modulo(a, modulus) {\n  return new AnimatedModulo(a, modulus);\n};\nvar diffClamp = function diffClamp(a, min, max) {\n  return new AnimatedDiffClamp(a, min, max);\n};\nvar _combineCallbacks = function _combineCallbacks(callback, config) {\n  if (callback && config.onComplete) {\n    return function () {\n      config.onComplete && config.onComplete(...arguments);\n      callback && callback(...arguments);\n    };\n  } else {\n    return callback || config.onComplete;\n  }\n};\nvar maybeVectorAnim = function maybeVectorAnim(value, config, anim) {\n  if (value instanceof AnimatedValueXY) {\n    var configX = _objectSpread({}, config);\n    var configY = _objectSpread({}, config);\n    for (var key in config) {\n      var _config$key = config[key],\n        x = _config$key.x,\n        y = _config$key.y;\n      if (x !== undefined && y !== undefined) {\n        configX[key] = x;\n        configY[key] = y;\n      }\n    }\n    var aX = anim(value.x, configX);\n    var aY = anim(value.y, configY);\n    // We use `stopTogether: false` here because otherwise tracking will break\n    // because the second animation will get stopped before it can update.\n    return parallel([aX, aY], {\n      stopTogether: false\n    });\n  } else if (value instanceof AnimatedColor) {\n    var configR = _objectSpread({}, config);\n    var configG = _objectSpread({}, config);\n    var configB = _objectSpread({}, config);\n    var configA = _objectSpread({}, config);\n    for (var _key in config) {\n      var _config$_key = config[_key],\n        r = _config$_key.r,\n        g = _config$_key.g,\n        b = _config$_key.b,\n        a = _config$_key.a;\n      if (r !== undefined && g !== undefined && b !== undefined && a !== undefined) {\n        configR[_key] = r;\n        configG[_key] = g;\n        configB[_key] = b;\n        configA[_key] = a;\n      }\n    }\n    var aR = anim(value.r, configR);\n    var aG = anim(value.g, configG);\n    var aB = anim(value.b, configB);\n    var aA = anim(value.a, configA);\n    // We use `stopTogether: false` here because otherwise tracking will break\n    // because the second animation will get stopped before it can update.\n    return parallel([aR, aG, aB, aA], {\n      stopTogether: false\n    });\n  }\n  return null;\n};\nvar spring = function spring(value, config) {\n  var _start = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(new AnimatedTracking(singleValue, configuration.toValue, SpringAnimation, singleConfig, callback));\n    } else {\n      singleValue.animate(new SpringAnimation(singleConfig), callback);\n    }\n  };\n  return maybeVectorAnim(value, config, spring) || {\n    start: function start(callback) {\n      _start(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations\n      });\n      _start(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar timing = function timing(value, config) {\n  var _start2 = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    if (configuration.toValue instanceof AnimatedNode) {\n      singleValue.track(new AnimatedTracking(singleValue, configuration.toValue, TimingAnimation, singleConfig, callback));\n    } else {\n      singleValue.animate(new TimingAnimation(singleConfig), callback);\n    }\n  };\n  return maybeVectorAnim(value, config, timing) || {\n    start: function start(callback) {\n      _start2(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations\n      });\n      _start2(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar decay = function decay(value, config) {\n  var _start3 = function start(animatedValue, configuration, callback) {\n    callback = _combineCallbacks(callback, configuration);\n    var singleValue = animatedValue;\n    var singleConfig = configuration;\n    singleValue.stopTracking();\n    singleValue.animate(new DecayAnimation(singleConfig), callback);\n  };\n  return maybeVectorAnim(value, config, decay) || {\n    start: function start(callback) {\n      _start3(value, config, callback);\n    },\n    stop: function stop() {\n      value.stopAnimation();\n    },\n    reset: function reset() {\n      value.resetAnimation();\n    },\n    _startNativeLoop: function _startNativeLoop(iterations) {\n      var singleConfig = _objectSpread(_objectSpread({}, config), {}, {\n        iterations\n      });\n      _start3(value, singleConfig);\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return config.useNativeDriver || false;\n    }\n  };\n};\nvar sequence = function sequence(animations) {\n  var current = 0;\n  return {\n    start: function start(callback) {\n      var onComplete = function onComplete(result) {\n        if (!result.finished) {\n          callback && callback(result);\n          return;\n        }\n        current++;\n        if (current === animations.length) {\n          callback && callback(result);\n          return;\n        }\n        animations[current].start(onComplete);\n      };\n      if (animations.length === 0) {\n        callback && callback({\n          finished: true\n        });\n      } else {\n        animations[current].start(onComplete);\n      }\n    },\n    stop: function stop() {\n      if (current < animations.length) {\n        animations[current].stop();\n      }\n    },\n    reset: function reset() {\n      animations.forEach((animation, idx) => {\n        if (idx <= current) {\n          animation.reset();\n        }\n      });\n      current = 0;\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.sequence animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return false;\n    }\n  };\n};\nvar parallel = function parallel(animations, config) {\n  var doneCount = 0;\n  // Make sure we only call stop() at most once for each animation\n  var hasEnded = {};\n  var stopTogether = !(config && config.stopTogether === false);\n  var result = {\n    start: function start(callback) {\n      if (doneCount === animations.length) {\n        callback && callback({\n          finished: true\n        });\n        return;\n      }\n      animations.forEach((animation, idx) => {\n        var cb = function cb(endResult) {\n          hasEnded[idx] = true;\n          doneCount++;\n          if (doneCount === animations.length) {\n            doneCount = 0;\n            callback && callback(endResult);\n            return;\n          }\n          if (!endResult.finished && stopTogether) {\n            result.stop();\n          }\n        };\n        if (!animation) {\n          cb({\n            finished: true\n          });\n        } else {\n          animation.start(cb);\n        }\n      });\n    },\n    stop: function stop() {\n      animations.forEach((animation, idx) => {\n        !hasEnded[idx] && animation.stop();\n        hasEnded[idx] = true;\n      });\n    },\n    reset: function reset() {\n      animations.forEach((animation, idx) => {\n        animation.reset();\n        hasEnded[idx] = false;\n        doneCount = 0;\n      });\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.parallel animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return false;\n    }\n  };\n  return result;\n};\nvar delay = function delay(time) {\n  // Would be nice to make a specialized implementation\n  return timing(new AnimatedValue(0), {\n    toValue: 0,\n    delay: time,\n    duration: 0,\n    useNativeDriver: false\n  });\n};\nvar stagger = function stagger(time, animations) {\n  return parallel(animations.map((animation, i) => {\n    return sequence([delay(time * i), animation]);\n  }));\n};\nvar loop = function loop(animation, // $FlowFixMe[prop-missing]\n_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$iterations = _ref.iterations,\n    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations,\n    _ref$resetBeforeItera = _ref.resetBeforeIteration,\n    resetBeforeIteration = _ref$resetBeforeItera === void 0 ? true : _ref$resetBeforeItera;\n  var isFinished = false;\n  var iterationsSoFar = 0;\n  return {\n    start: function start(callback) {\n      var restart = function restart(result) {\n        if (result === void 0) {\n          result = {\n            finished: true\n          };\n        }\n        if (isFinished || iterationsSoFar === iterations || result.finished === false) {\n          callback && callback(result);\n        } else {\n          iterationsSoFar++;\n          resetBeforeIteration && animation.reset();\n          animation.start(restart);\n        }\n      };\n      if (!animation || iterations === 0) {\n        callback && callback({\n          finished: true\n        });\n      } else {\n        if (animation._isUsingNativeDriver()) {\n          animation._startNativeLoop(iterations);\n        } else {\n          restart(); // Start looping recursively on the js thread\n        }\n      }\n    },\n    stop: function stop() {\n      isFinished = true;\n      animation.stop();\n    },\n    reset: function reset() {\n      iterationsSoFar = 0;\n      isFinished = false;\n      animation.reset();\n    },\n    _startNativeLoop: function _startNativeLoop() {\n      throw new Error('Loops run using the native driver cannot contain Animated.loop animations');\n    },\n    _isUsingNativeDriver: function _isUsingNativeDriver() {\n      return animation._isUsingNativeDriver();\n    }\n  };\n};\nfunction forkEvent(event, listener) {\n  if (!event) {\n    return listener;\n  } else if (event instanceof AnimatedEvent) {\n    event.__addListener(listener);\n    return event;\n  } else {\n    return function () {\n      typeof event === 'function' && event(...arguments);\n      listener(...arguments);\n    };\n  }\n}\nfunction unforkEvent(event, listener) {\n  if (event && event instanceof AnimatedEvent) {\n    event.__removeListener(listener);\n  }\n}\nvar event = function event(argMapping, config) {\n  var animatedEvent = new AnimatedEvent(argMapping, config);\n  if (animatedEvent.__isNative) {\n    return animatedEvent;\n  } else {\n    return animatedEvent.__getHandler();\n  }\n};\n\n// All types of animated nodes that represent scalar numbers and can be interpolated (etc)\n\n/**\n * The `Animated` library is designed to make animations fluid, powerful, and\n * easy to build and maintain. `Animated` focuses on declarative relationships\n * between inputs and outputs, with configurable transforms in between, and\n * simple `start`/`stop` methods to control time-based animation execution.\n * If additional transforms are added, be sure to include them in\n * AnimatedMock.js as well.\n *\n * See https://reactnative.dev/docs/animated\n */\nexport default {\n  /**\n   * Standard value class for driving animations.  Typically initialized with\n   * `new Animated.Value(0);`\n   *\n   * See https://reactnative.dev/docs/animated#value\n   */\n  Value: AnimatedValue,\n  /**\n   * 2D value class for driving 2D animations, such as pan gestures.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy\n   */\n  ValueXY: AnimatedValueXY,\n  /**\n   * Value class for driving color animations.\n   */\n  Color: AnimatedColor,\n  /**\n   * Exported to use the Interpolation type in flow.\n   *\n   * See https://reactnative.dev/docs/animated#interpolation\n   */\n  Interpolation: AnimatedInterpolation,\n  /**\n   * Exported for ease of type checking. All animated values derive from this\n   * class.\n   *\n   * See https://reactnative.dev/docs/animated#node\n   */\n  Node: AnimatedNode,\n  /**\n   * Animates a value from an initial velocity to zero based on a decay\n   * coefficient.\n   *\n   * See https://reactnative.dev/docs/animated#decay\n   */\n  decay,\n  /**\n   * Animates a value along a timed easing curve. The Easing module has tons of\n   * predefined curves, or you can use your own function.\n   *\n   * See https://reactnative.dev/docs/animated#timing\n   */\n  timing,\n  /**\n   * Animates a value according to an analytical spring model based on\n   * damped harmonic oscillation.\n   *\n   * See https://reactnative.dev/docs/animated#spring\n   */\n  spring,\n  /**\n   * Creates a new Animated value composed from two Animated values added\n   * together.\n   *\n   * See https://reactnative.dev/docs/animated#add\n   */\n  add,\n  /**\n   * Creates a new Animated value composed by subtracting the second Animated\n   * value from the first Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#subtract\n   */\n  subtract,\n  /**\n   * Creates a new Animated value composed by dividing the first Animated value\n   * by the second Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#divide\n   */\n  divide,\n  /**\n   * Creates a new Animated value composed from two Animated values multiplied\n   * together.\n   *\n   * See https://reactnative.dev/docs/animated#multiply\n   */\n  multiply,\n  /**\n   * Creates a new Animated value that is the (non-negative) modulo of the\n   * provided Animated value.\n   *\n   * See https://reactnative.dev/docs/animated#modulo\n   */\n  modulo,\n  /**\n   * Create a new Animated value that is limited between 2 values. It uses the\n   * difference between the last value so even if the value is far from the\n   * bounds it will start changing when the value starts getting closer again.\n   *\n   * See https://reactnative.dev/docs/animated#diffclamp\n   */\n  diffClamp,\n  /**\n   * Starts an animation after the given delay.\n   *\n   * See https://reactnative.dev/docs/animated#delay\n   */\n  delay,\n  /**\n   * Starts an array of animations in order, waiting for each to complete\n   * before starting the next. If the current running animation is stopped, no\n   * following animations will be started.\n   *\n   * See https://reactnative.dev/docs/animated#sequence\n   */\n  sequence,\n  /**\n   * Starts an array of animations all at the same time. By default, if one\n   * of the animations is stopped, they will all be stopped. You can override\n   * this with the `stopTogether` flag.\n   *\n   * See https://reactnative.dev/docs/animated#parallel\n   */\n  parallel,\n  /**\n   * Array of animations may run in parallel (overlap), but are started in\n   * sequence with successive delays.  Nice for doing trailing effects.\n   *\n   * See https://reactnative.dev/docs/animated#stagger\n   */\n  stagger,\n  /**\n   * Loops a given animation continuously, so that each time it reaches the\n   * end, it resets and begins again from the start.\n   *\n   * See https://reactnative.dev/docs/animated#loop\n   */\n  loop,\n  /**\n   * Takes an array of mappings and extracts values from each arg accordingly,\n   * then calls `setValue` on the mapped outputs.\n   *\n   * See https://reactnative.dev/docs/animated#event\n   */\n  event,\n  /**\n   * Make any React component Animatable.  Used to create `Animated.View`, etc.\n   *\n   * See https://reactnative.dev/docs/animated#createanimatedcomponent\n   */\n  createAnimatedComponent,\n  /**\n   * Imperative API to attach an animated value to an event on a view. Prefer\n   * using `Animated.event` with `useNativeDrive: true` if possible.\n   *\n   * See https://reactnative.dev/docs/animated#attachnativeevent\n   */\n  attachNativeEvent,\n  /**\n   * Advanced imperative API for snooping on animated events that are passed in\n   * through props. Use values directly where possible.\n   *\n   * See https://reactnative.dev/docs/animated#forkevent\n   */\n  forkEvent,\n  unforkEvent,\n  /**\n   * Expose Event class, so it can be used as a type for type checkers.\n   */\n  Event: AnimatedEvent\n};"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,SAASC,aAAa,EAAEC,iBAAiB;AACzC,OAAOC,gBAAgB;AACvB,OAAOC,iBAAiB;AACxB,OAAOC,gBAAgB;AACvB,OAAOC,qBAAqB;AAC5B,OAAOC,cAAc;AACrB,OAAOC,sBAAsB;AAC7B,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,mBAAmB;AAC1B,OAAOC,gBAAgB;AACvB,OAAOC,aAAa;AACpB,OAAOC,eAAe;AACtB,OAAOC,cAAc;AACrB,OAAOC,eAAe;AACtB,OAAOC,eAAe;AACtB,OAAOC,uBAAuB;AAC9B,OAAOC,aAAa;AACpB,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAO,IAAInB,gBAAgB,CAACkB,CAAC,EAAEC,CAAC,CAAC;AACnC,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACrC,OAAO,IAAIX,mBAAmB,CAACU,CAAC,EAAEC,CAAC,CAAC;AACtC,CAAC;AACD,IAAIE,MAAM,GAAG,SAASA,MAAMA,CAACH,CAAC,EAAEC,CAAC,EAAE;EACjC,OAAO,IAAIjB,gBAAgB,CAACgB,CAAC,EAAEC,CAAC,CAAC;AACnC,CAAC;AACD,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACrC,OAAO,IAAId,sBAAsB,CAACa,CAAC,EAAEC,CAAC,CAAC;AACzC,CAAC;AACD,IAAII,MAAM,GAAG,SAASA,MAAMA,CAACL,CAAC,EAAEM,OAAO,EAAE;EACvC,OAAO,IAAIpB,cAAc,CAACc,CAAC,EAAEM,OAAO,CAAC;AACvC,CAAC;AACD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACP,CAAC,EAAEQ,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,IAAI1B,iBAAiB,CAACiB,CAAC,EAAEQ,GAAG,EAAEC,GAAG,CAAC;AAC3C,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACnE,IAAID,QAAQ,IAAIC,MAAM,CAACC,UAAU,EAAE;IACjC,OAAO,YAAY;MACjBD,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAAAC,KAAA,CAAjBF,MAAM,EAAeG,SAAS,CAAC;MACpDJ,QAAQ,IAAIA,QAAQ,CAAAG,KAAA,SAAIC,SAAS,CAAC;IACpC,CAAC;EACH,CAAC,MAAM;IACL,OAAOJ,QAAQ,IAAIC,MAAM,CAACC,UAAU;EACtC;AACF,CAAC;AACD,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEL,MAAM,EAAEM,IAAI,EAAE;EAClE,IAAID,KAAK,YAAYxB,eAAe,EAAE;IACpC,IAAI0B,OAAO,GAAGxC,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,IAAIQ,OAAO,GAAGzC,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,KAAK,IAAIS,GAAG,IAAIT,MAAM,EAAE;MACtB,IAAIU,WAAW,GAAGV,MAAM,CAACS,GAAG,CAAC;QAC3BE,CAAC,GAAGD,WAAW,CAACC,CAAC;QACjBC,CAAC,GAAGF,WAAW,CAACE,CAAC;MACnB,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,EAAE;QACtCN,OAAO,CAACE,GAAG,CAAC,GAAGE,CAAC;QAChBH,OAAO,CAACC,GAAG,CAAC,GAAGG,CAAC;MAClB;IACF;IACA,IAAIE,EAAE,GAAGR,IAAI,CAACD,KAAK,CAACM,CAAC,EAAEJ,OAAO,CAAC;IAC/B,IAAIQ,EAAE,GAAGT,IAAI,CAACD,KAAK,CAACO,CAAC,EAAEJ,OAAO,CAAC;IAG/B,OAAOQ,QAAQ,CAAC,CAACF,EAAE,EAAEC,EAAE,CAAC,EAAE;MACxBE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIZ,KAAK,YAAYnB,aAAa,EAAE;IACzC,IAAIgC,OAAO,GAAGnD,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,IAAImB,OAAO,GAAGpD,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,IAAIoB,OAAO,GAAGrD,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,IAAIqB,OAAO,GAAGtD,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;IACvC,KAAK,IAAIsB,IAAI,IAAItB,MAAM,EAAE;MACvB,IAAIuB,YAAY,GAAGvB,MAAM,CAACsB,IAAI,CAAC;QAC7BE,CAAC,GAAGD,YAAY,CAACC,CAAC;QAClBC,CAAC,GAAGF,YAAY,CAACE,CAAC;QAClBpC,CAAC,GAAGkC,YAAY,CAAClC,CAAC;QAClBD,CAAC,GAAGmC,YAAY,CAACnC,CAAC;MACpB,IAAIoC,CAAC,KAAKX,SAAS,IAAIY,CAAC,KAAKZ,SAAS,IAAIxB,CAAC,KAAKwB,SAAS,IAAIzB,CAAC,KAAKyB,SAAS,EAAE;QAC5EK,OAAO,CAACI,IAAI,CAAC,GAAGE,CAAC;QACjBL,OAAO,CAACG,IAAI,CAAC,GAAGG,CAAC;QACjBL,OAAO,CAACE,IAAI,CAAC,GAAGjC,CAAC;QACjBgC,OAAO,CAACC,IAAI,CAAC,GAAGlC,CAAC;MACnB;IACF;IACA,IAAIsC,EAAE,GAAGpB,IAAI,CAACD,KAAK,CAACmB,CAAC,EAAEN,OAAO,CAAC;IAC/B,IAAIS,EAAE,GAAGrB,IAAI,CAACD,KAAK,CAACoB,CAAC,EAAEN,OAAO,CAAC;IAC/B,IAAIS,EAAE,GAAGtB,IAAI,CAACD,KAAK,CAAChB,CAAC,EAAE+B,OAAO,CAAC;IAC/B,IAAIS,EAAE,GAAGvB,IAAI,CAACD,KAAK,CAACjB,CAAC,EAAEiC,OAAO,CAAC;IAG/B,OAAOL,QAAQ,CAAC,CAACU,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE;MAChCZ,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIa,MAAM,GAAG,SAASA,MAAMA,CAACzB,KAAK,EAAEL,MAAM,EAAE;EAC1C,IAAI+B,MAAM,GAAG,SAASC,KAAKA,CAACC,aAAa,EAAEC,aAAa,EAAEnC,QAAQ,EAAE;IAClEA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAIC,WAAW,GAAGF,aAAa;IAC/B,IAAIG,YAAY,GAAGF,aAAa;IAChCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1B,IAAIH,aAAa,CAACI,OAAO,YAAY9D,YAAY,EAAE;MACjD2D,WAAW,CAACI,KAAK,CAAC,IAAI5D,gBAAgB,CAACwD,WAAW,EAAED,aAAa,CAACI,OAAO,EAAEvD,eAAe,EAAEqD,YAAY,EAAErC,QAAQ,CAAC,CAAC;IACtH,CAAC,MAAM;MACLoC,WAAW,CAACK,OAAO,CAAC,IAAIzD,eAAe,CAACqD,YAAY,CAAC,EAAErC,QAAQ,CAAC;IAClE;EACF,CAAC;EACD,OAAOK,eAAe,CAACC,KAAK,EAAEL,MAAM,EAAE8B,MAAM,CAAC,IAAI;IAC/CE,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9BgC,MAAM,CAAC1B,KAAK,EAAEL,MAAM,EAAED,QAAQ,CAAC;IACjC,CAAC;IACD0C,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBpC,KAAK,CAACqC,aAAa,CAAC,CAAC;IACvB,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBtC,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB,CAAC;IACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,UAAU,EAAE;MACtD,IAAIV,YAAY,GAAGrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9D8C,UAAU,EAAVA;MACF,CAAC,CAAC;MACFf,MAAM,CAAC1B,KAAK,EAAE+B,YAAY,CAAC;IAC7B,CAAC;IACDW,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAO/C,MAAM,CAACgD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AACH,CAAC;AACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAC5C,KAAK,EAAEL,MAAM,EAAE;EAC1C,IAAIkD,OAAO,GAAG,SAASlB,KAAKA,CAACC,aAAa,EAAEC,aAAa,EAAEnC,QAAQ,EAAE;IACnEA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAIC,WAAW,GAAGF,aAAa;IAC/B,IAAIG,YAAY,GAAGF,aAAa;IAChCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1B,IAAIH,aAAa,CAACI,OAAO,YAAY9D,YAAY,EAAE;MACjD2D,WAAW,CAACI,KAAK,CAAC,IAAI5D,gBAAgB,CAACwD,WAAW,EAAED,aAAa,CAACI,OAAO,EAAEtD,eAAe,EAAEoD,YAAY,EAAErC,QAAQ,CAAC,CAAC;IACtH,CAAC,MAAM;MACLoC,WAAW,CAACK,OAAO,CAAC,IAAIxD,eAAe,CAACoD,YAAY,CAAC,EAAErC,QAAQ,CAAC;IAClE;EACF,CAAC;EACD,OAAOK,eAAe,CAACC,KAAK,EAAEL,MAAM,EAAEiD,MAAM,CAAC,IAAI;IAC/CjB,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9BmD,OAAO,CAAC7C,KAAK,EAAEL,MAAM,EAAED,QAAQ,CAAC;IAClC,CAAC;IACD0C,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBpC,KAAK,CAACqC,aAAa,CAAC,CAAC;IACvB,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBtC,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB,CAAC;IACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,UAAU,EAAE;MACtD,IAAIV,YAAY,GAAGrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9D8C,UAAU,EAAVA;MACF,CAAC,CAAC;MACFI,OAAO,CAAC7C,KAAK,EAAE+B,YAAY,CAAC;IAC9B,CAAC;IACDW,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAO/C,MAAM,CAACgD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AACH,CAAC;AACD,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAC9C,KAAK,EAAEL,MAAM,EAAE;EACxC,IAAIoD,OAAO,GAAG,SAASpB,KAAKA,CAACC,aAAa,EAAEC,aAAa,EAAEnC,QAAQ,EAAE;IACnEA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,EAAEmC,aAAa,CAAC;IACrD,IAAIC,WAAW,GAAGF,aAAa;IAC/B,IAAIG,YAAY,GAAGF,aAAa;IAChCC,WAAW,CAACE,YAAY,CAAC,CAAC;IAC1BF,WAAW,CAACK,OAAO,CAAC,IAAI1D,cAAc,CAACsD,YAAY,CAAC,EAAErC,QAAQ,CAAC;EACjE,CAAC;EACD,OAAOK,eAAe,CAACC,KAAK,EAAEL,MAAM,EAAEmD,KAAK,CAAC,IAAI;IAC9CnB,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9BqD,OAAO,CAAC/C,KAAK,EAAEL,MAAM,EAAED,QAAQ,CAAC;IAClC,CAAC;IACD0C,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBpC,KAAK,CAACqC,aAAa,CAAC,CAAC;IACvB,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBtC,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB,CAAC;IACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,UAAU,EAAE;MACtD,IAAIV,YAAY,GAAGrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9D8C,UAAU,EAAVA;MACF,CAAC,CAAC;MACFM,OAAO,CAAC/C,KAAK,EAAE+B,YAAY,CAAC;IAC9B,CAAC;IACDW,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAO/C,MAAM,CAACgD,eAAe,IAAI,KAAK;IACxC;EACF,CAAC;AACH,CAAC;AACD,IAAIK,QAAQ,GAAG,SAASA,QAAQA,CAACC,UAAU,EAAE;EAC3C,IAAIC,OAAO,GAAG,CAAC;EACf,OAAO;IACLvB,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9B,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAACuD,MAAM,EAAE;QAC3C,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;UACpB1D,QAAQ,IAAIA,QAAQ,CAACyD,MAAM,CAAC;UAC5B;QACF;QACAD,OAAO,EAAE;QACT,IAAIA,OAAO,KAAKD,UAAU,CAACI,MAAM,EAAE;UACjC3D,QAAQ,IAAIA,QAAQ,CAACyD,MAAM,CAAC;UAC5B;QACF;QACAF,UAAU,CAACC,OAAO,CAAC,CAACvB,KAAK,CAAC/B,UAAU,CAAC;MACvC,CAAC;MACD,IAAIqD,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;QAC3B3D,QAAQ,IAAIA,QAAQ,CAAC;UACnB0D,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLH,UAAU,CAACC,OAAO,CAAC,CAACvB,KAAK,CAAC/B,UAAU,CAAC;MACvC;IACF,CAAC;IACDwC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpB,IAAIc,OAAO,GAAGD,UAAU,CAACI,MAAM,EAAE;QAC/BJ,UAAU,CAACC,OAAO,CAAC,CAACd,IAAI,CAAC,CAAC;MAC5B;IACF,CAAC;IACDE,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBW,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,IAAIA,GAAG,IAAIN,OAAO,EAAE;UAClBK,SAAS,CAACjB,KAAK,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;MACFY,OAAO,GAAG,CAAC;IACb,CAAC;IACDV,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;MAC5C,MAAM,IAAIiB,KAAK,CAAC,+EAA+E,CAAC;IAClG,CAAC;IACDf,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAO,KAAK;IACd;EACF,CAAC;AACH,CAAC;AACD,IAAI/B,QAAQ,GAAG,SAASA,QAAQA,CAACsC,UAAU,EAAEtD,MAAM,EAAE;EACnD,IAAI+D,SAAS,GAAG,CAAC;EAEjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI/C,YAAY,GAAG,EAAEjB,MAAM,IAAIA,MAAM,CAACiB,YAAY,KAAK,KAAK,CAAC;EAC7D,IAAIuC,MAAM,GAAG;IACXxB,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9B,IAAIgE,SAAS,KAAKT,UAAU,CAACI,MAAM,EAAE;QACnC3D,QAAQ,IAAIA,QAAQ,CAAC;UACnB0D,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MACAH,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,IAAII,EAAE,GAAG,SAASA,EAAEA,CAACC,SAAS,EAAE;UAC9BF,QAAQ,CAACH,GAAG,CAAC,GAAG,IAAI;UACpBE,SAAS,EAAE;UACX,IAAIA,SAAS,KAAKT,UAAU,CAACI,MAAM,EAAE;YACnCK,SAAS,GAAG,CAAC;YACbhE,QAAQ,IAAIA,QAAQ,CAACmE,SAAS,CAAC;YAC/B;UACF;UACA,IAAI,CAACA,SAAS,CAACT,QAAQ,IAAIxC,YAAY,EAAE;YACvCuC,MAAM,CAACf,IAAI,CAAC,CAAC;UACf;QACF,CAAC;QACD,IAAI,CAACmB,SAAS,EAAE;UACdK,EAAE,CAAC;YACDR,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLG,SAAS,CAAC5B,KAAK,CAACiC,EAAE,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC;IACDxB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBa,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrC,CAACG,QAAQ,CAACH,GAAG,CAAC,IAAID,SAAS,CAACnB,IAAI,CAAC,CAAC;QAClCuB,QAAQ,CAACH,GAAG,CAAC,GAAG,IAAI;MACtB,CAAC,CAAC;IACJ,CAAC;IACDlB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBW,UAAU,CAACK,OAAO,CAAC,UAACC,SAAS,EAAEC,GAAG,EAAK;QACrCD,SAAS,CAACjB,KAAK,CAAC,CAAC;QACjBqB,QAAQ,CAACH,GAAG,CAAC,GAAG,KAAK;QACrBE,SAAS,GAAG,CAAC;MACf,CAAC,CAAC;IACJ,CAAC;IACDlB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;MAC5C,MAAM,IAAIiB,KAAK,CAAC,+EAA+E,CAAC;IAClG,CAAC;IACDf,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAO,KAAK;IACd;EACF,CAAC;EACD,OAAOS,MAAM;AACf,CAAC;AACD,IAAIW,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAE/B,OAAOnB,MAAM,CAAC,IAAIrE,aAAa,CAAC,CAAC,CAAC,EAAE;IAClC0D,OAAO,EAAE,CAAC;IACV6B,KAAK,EAAEC,IAAI;IACXC,QAAQ,EAAE,CAAC;IACXrB,eAAe,EAAE;EACnB,CAAC,CAAC;AACJ,CAAC;AACD,IAAIsB,OAAO,GAAG,SAASA,OAAOA,CAACF,IAAI,EAAEd,UAAU,EAAE;EAC/C,OAAOtC,QAAQ,CAACsC,UAAU,CAACiB,GAAG,CAAC,UAACX,SAAS,EAAEY,CAAC,EAAK;IAC/C,OAAOnB,QAAQ,CAAC,CAACc,KAAK,CAACC,IAAI,GAAGI,CAAC,CAAC,EAAEZ,SAAS,CAAC,CAAC;EAC/C,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIa,IAAI,GAAG,SAASA,IAAIA,CAACb,SAAS,EAClCc,KAAK,EAAE;EACL,IAAIC,IAAI,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;IACtCE,eAAe,GAAGD,IAAI,CAAC7B,UAAU;IACjCA,UAAU,GAAG8B,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;IAC9DC,qBAAqB,GAAGF,IAAI,CAACG,oBAAoB;IACjDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;EACxF,IAAIE,UAAU,GAAG,KAAK;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,OAAO;IACLhD,KAAK,EAAE,SAASA,KAAKA,CAACjC,QAAQ,EAAE;MAC9B,IAAIkF,OAAO,GAAG,SAASA,OAAOA,CAACzB,MAAM,EAAE;QACrC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;UACrBA,MAAM,GAAG;YACPC,QAAQ,EAAE;UACZ,CAAC;QACH;QACA,IAAIsB,UAAU,IAAIC,eAAe,KAAKlC,UAAU,IAAIU,MAAM,CAACC,QAAQ,KAAK,KAAK,EAAE;UAC7E1D,QAAQ,IAAIA,QAAQ,CAACyD,MAAM,CAAC;QAC9B,CAAC,MAAM;UACLwB,eAAe,EAAE;UACjBF,oBAAoB,IAAIlB,SAAS,CAACjB,KAAK,CAAC,CAAC;UACzCiB,SAAS,CAAC5B,KAAK,CAACiD,OAAO,CAAC;QAC1B;MACF,CAAC;MACD,IAAI,CAACrB,SAAS,IAAId,UAAU,KAAK,CAAC,EAAE;QAClC/C,QAAQ,IAAIA,QAAQ,CAAC;UACnB0D,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIG,SAAS,CAACb,oBAAoB,CAAC,CAAC,EAAE;UACpCa,SAAS,CAACf,gBAAgB,CAACC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLmC,OAAO,CAAC,CAAC;QACX;MACF;IACF,CAAC;IACDxC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpBsC,UAAU,GAAG,IAAI;MACjBnB,SAAS,CAACnB,IAAI,CAAC,CAAC;IAClB,CAAC;IACDE,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBqC,eAAe,GAAG,CAAC;MACnBD,UAAU,GAAG,KAAK;MAClBnB,SAAS,CAACjB,KAAK,CAAC,CAAC;IACnB,CAAC;IACDE,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;MAC5C,MAAM,IAAIiB,KAAK,CAAC,2EAA2E,CAAC;IAC9F,CAAC;IACDf,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;MACpD,OAAOa,SAAS,CAACb,oBAAoB,CAAC,CAAC;IACzC;EACF,CAAC;AACH,CAAC;AACD,SAASmC,SAASA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAClC,IAAI,CAACD,KAAK,EAAE;IACV,OAAOC,QAAQ;EACjB,CAAC,MAAM,IAAID,KAAK,YAAYnH,aAAa,EAAE;IACzCmH,KAAK,CAACE,aAAa,CAACD,QAAQ,CAAC;IAC7B,OAAOD,KAAK;EACd,CAAC,MAAM;IACL,OAAO,YAAY;MACjB,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAAjF,KAAA,SAAIC,SAAS,CAAC;MAClDiF,QAAQ,CAAAlF,KAAA,SAAIC,SAAS,CAAC;IACxB,CAAC;EACH;AACF;AACA,SAASmF,WAAWA,CAACH,KAAK,EAAEC,QAAQ,EAAE;EACpC,IAAID,KAAK,IAAIA,KAAK,YAAYnH,aAAa,EAAE;IAC3CmH,KAAK,CAACI,gBAAgB,CAACH,QAAQ,CAAC;EAClC;AACF;AACA,IAAID,KAAK,GAAG,SAASA,KAAKA,CAACK,UAAU,EAAExF,MAAM,EAAE;EAC7C,IAAIyF,aAAa,GAAG,IAAIzH,aAAa,CAACwH,UAAU,EAAExF,MAAM,CAAC;EACzD,IAAIyF,aAAa,CAACC,UAAU,EAAE;IAC5B,OAAOD,aAAa;EACtB,CAAC,MAAM;IACL,OAAOA,aAAa,CAACE,YAAY,CAAC,CAAC;EACrC;AACF,CAAC;AAcD,eAAe;EAObC,KAAK,EAAEhH,aAAa;EAMpBiH,OAAO,EAAEhH,eAAe;EAIxBiH,KAAK,EAAE5G,aAAa;EAMpB6G,aAAa,EAAE1H,qBAAqB;EAOpC2H,IAAI,EAAExH,YAAY;EAOlB2E,KAAK,EAALA,KAAK;EAOLF,MAAM,EAANA,MAAM;EAONnB,MAAM,EAANA,MAAM;EAON3C,GAAG,EAAHA,GAAG;EAOHG,QAAQ,EAARA,QAAQ;EAORC,MAAM,EAANA,MAAM;EAONC,QAAQ,EAARA,QAAQ;EAORC,MAAM,EAANA,MAAM;EAQNE,SAAS,EAATA,SAAS;EAMTwE,KAAK,EAALA,KAAK;EAQLd,QAAQ,EAARA,QAAQ;EAQRrC,QAAQ,EAARA,QAAQ;EAORsD,OAAO,EAAPA,OAAO;EAOPG,IAAI,EAAJA,IAAI;EAOJU,KAAK,EAALA,KAAK;EAMLlG,uBAAuB,EAAvBA,uBAAuB;EAOvBhB,iBAAiB,EAAjBA,iBAAiB;EAOjBiH,SAAS,EAATA,SAAS;EACTI,WAAW,EAAXA,WAAW;EAIXW,KAAK,EAAEjI;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}