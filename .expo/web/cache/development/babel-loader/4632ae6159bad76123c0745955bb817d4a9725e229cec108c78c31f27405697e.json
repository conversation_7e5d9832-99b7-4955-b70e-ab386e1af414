{"ast": null, "code": "var rtlScripts = new Set(['Arab', 'Syrc', '<PERSON>r', 'Mand', 'Thaa', 'Mend', 'Nko<PERSON>', 'Ad<PERSON>', 'Rohg', 'Hebr']);\nvar rtlLangs = new Set(['ae', 'ar', 'arc', 'bcc', 'bqi', 'ckb', 'dv', 'fa', 'far', 'glk', 'he', 'iw', 'khw', 'ks', 'ku', 'mzn', 'nqo', 'pnb', 'ps', 'sd', 'ug', 'ur', 'yi']);\nvar cache = new Map();\nexport function isLocaleRTL(locale) {\n  var cachedRTL = cache.get(locale);\n  if (cachedRTL) {\n    return cachedRTL;\n  }\n  var isRTL = false;\n  if (Intl.Locale) {\n    try {\n      var script = new Intl.Locale(locale).maximize().script;\n      isRTL = rtlScripts.has(script);\n    } catch (_unused) {\n      var lang = locale.split('-')[0];\n      isRTL = rtlLangs.has(lang);\n    }\n  } else {\n    var _lang = locale.split('-')[0];\n    isRTL = rtlLangs.has(_lang);\n  }\n  cache.set(locale, isRTL);\n  return isRTL;\n}", "map": {"version": 3, "names": ["rtlScripts", "Set", "rtlLangs", "cache", "Map", "isLocaleRTL", "locale", "cachedRTL", "get", "isRTL", "Intl", "Locale", "script", "maximize", "has", "_unused", "lang", "split", "_lang", "set"], "sources": ["/Users/<USER>/Desktop/PlantPal/node_modules/react-native-web/dist/modules/useLocale/isLocaleRTL.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar rtlScripts = new Set(['Arab', 'Syrc', 'Samr', 'Mand', 'Thaa', 'Mend', '<PERSON><PERSON>o', '<PERSON><PERSON>', 'Rohg', 'Hebr']);\nvar rtlLangs = new Set(['ae',\n// Avestan\n'ar',\n// Arabic\n'arc',\n// Aramaic\n'bcc',\n// Southern Balochi\n'bqi',\n// Bakthiari\n'ckb',\n// Sorani\n'dv',\n// Dhivehi\n'fa', 'far',\n// Persian\n'glk',\n// <PERSON>aki\n'he', 'iw',\n// Hebrew\n'khw',\n// Khowar\n'ks',\n// Kashmiri\n'ku',\n// Kurdish\n'mzn',\n// Mazanderani\n'nqo',\n// N'Ko\n'pnb',\n// Western Punjabi\n'ps',\n// Pashto\n'sd',\n// Sindhi\n'ug',\n// Uyghur\n'ur',\n// Urdu\n'yi' // Yiddish\n]);\nvar cache = new Map();\n\n/**\n * Determine the writing direction of a locale\n */\nexport function isLocaleRTL(locale) {\n  var cachedRTL = cache.get(locale);\n  if (cachedRTL) {\n    return cachedRTL;\n  }\n  var isRTL = false;\n  // $FlowFixMe\n  if (Intl.Locale) {\n    try {\n      // $FlowFixMe\n      var script = new Intl.Locale(locale).maximize().script;\n      isRTL = rtlScripts.has(script);\n    } catch (_unused) {\n      // RangeError: Incorrect locale information provided\n      // Fallback to inferring from language\n      var lang = locale.split('-')[0];\n      isRTL = rtlLangs.has(lang);\n    }\n  } else {\n    // Fallback to inferring from language\n    var _lang = locale.split('-')[0];\n    isRTL = rtlLangs.has(_lang);\n  }\n  cache.set(locale, isRTL);\n  return isRTL;\n}"], "mappings": "AASA,IAAIA,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1G,IAAIC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC,IAAI,EAE5B,IAAI,EAEJ,KAAK,EAEL,KAAK,EAEL,KAAK,EAEL,KAAK,EAEL,IAAI,EAEJ,IAAI,EAAE,KAAK,EAEX,KAAK,EAEL,IAAI,EAAE,IAAI,EAEV,KAAK,EAEL,IAAI,EAEJ,IAAI,EAEJ,KAAK,EAEL,KAAK,EAEL,KAAK,EAEL,IAAI,EAEJ,IAAI,EAEJ,IAAI,EAEJ,IAAI,EAEJ,IAAI,CACH,CAAC;AACF,IAAIE,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AAKrB,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,IAAIC,SAAS,GAAGJ,KAAK,CAACK,GAAG,CAACF,MAAM,CAAC;EACjC,IAAIC,SAAS,EAAE;IACb,OAAOA,SAAS;EAClB;EACA,IAAIE,KAAK,GAAG,KAAK;EAEjB,IAAIC,IAAI,CAACC,MAAM,EAAE;IACf,IAAI;MAEF,IAAIC,MAAM,GAAG,IAAIF,IAAI,CAACC,MAAM,CAACL,MAAM,CAAC,CAACO,QAAQ,CAAC,CAAC,CAACD,MAAM;MACtDH,KAAK,GAAGT,UAAU,CAACc,GAAG,CAACF,MAAM,CAAC;IAChC,CAAC,CAAC,OAAOG,OAAO,EAAE;MAGhB,IAAIC,IAAI,GAAGV,MAAM,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BR,KAAK,GAAGP,QAAQ,CAACY,GAAG,CAACE,IAAI,CAAC;IAC5B;EACF,CAAC,MAAM;IAEL,IAAIE,KAAK,GAAGZ,MAAM,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChCR,KAAK,GAAGP,QAAQ,CAACY,GAAG,CAACI,KAAK,CAAC;EAC7B;EACAf,KAAK,CAACgB,GAAG,CAACb,MAAM,EAAEG,KAAK,CAAC;EACxB,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}