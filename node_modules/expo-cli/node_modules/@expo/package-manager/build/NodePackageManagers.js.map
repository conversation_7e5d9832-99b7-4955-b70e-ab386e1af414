{"version": 3, "file": "NodePackageManagers.js", "sourceRoot": "", "sources": ["../src/NodePackageManagers.ts"], "names": [], "mappings": ";;;;;AAAA,gEAAuC;AACvC,oEAA6D;AAC7D,4DAAmC;AACnC,wFAAyD;AACzD,2BAAgC;AAChC,sEAA4C;AAC5C,gDAAwB;AACxB,oDAA4B;AAC5B,kDAA0B;AAC1B,mCAAmC;AAGnC,oFAA4D;AAE5D;;;GAGG;AACH,MAAM,aAAa,GAAG,EAAE,sBAAsB,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;AAEpE,MAAM,IAAI,GAAG,MAAM,oBAAS,EAAE,CAAC,MAAM,IAAI,CAAC;AAC1C,MAAM,+BAA+B,GAAG,IAAI,MAAM,CAChD,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,oDAAoD,EACxF,GAAG,CACJ,CAAC;AACF,MAAM,gCAAgC,GAAG,IAAI,MAAM,CACjD,GAAG,IAAI,UAAU,IAAI,+DAA+D,EACpF,GAAG,CACJ,CAAC;AAEF;;;;GAIG;AACH,SAAgB,WAAW,CAAC,WAAmB;IAC7C,MAAM,aAAa,GAAG,kCAAiB,CAAC,WAAW,CAAC,CAAC;IACrD,IAAI,aAAa,EAAE;QACjB,OAAO,eAAU,CAAC,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KAC1D;IACD,OAAO,eAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;AACzD,CAAC;AAND,kCAMC;AAED,MAAM,kBAAmB,SAAQ,kBAAS;IACxC,UAAU,CACR,KAAa,EACb,QAAgB,EAChB,QAAoD;QAEpD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC,CAAC;QACzE,QAAQ,EAAE,CAAC;IACb,CAAC;CACF;AAED,MAAM,mBAAoB,SAAQ,kBAAS;IACzC,UAAU,CACR,KAAa,EACb,QAAgB,EAChB,QAAoD;QAEpD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC1E,QAAQ,EAAE,CAAC;IACb,CAAC;CACF;AACD,MAAa,iBAAiB;IAK5B,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAmD;QAC/E,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO,mBACV,GAAG,kCACE,OAAO,CAAC,GAAG,GACX,aAAa,GAElB,GAAG,IACA,CAAC,MAAM;YACR,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE;YACvB,CAAC,CAAC;gBACE,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;aACtC,CAAC,CACP,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAG,KAAe;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAE9C,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;SACnC;QACD,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACnF;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAG,KAAe;QAClC,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAE9C,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;YACrD,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;SACnC;QACD,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACvF;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,qBAAU,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW;QAC9B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,qBAAU,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QACtF,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC3E;QACD,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;QACtE,IAAI,eAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC3B;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QACD,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACpE,IAAI,eAAU,CAAC,eAAe,CAAC,EAAE;YAC/B,gBAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC9B;IACH,CAAC;IAED,UAAU;IACF,KAAK,CAAC,SAAS,CAAC,IAAc;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACrC;QAED,0FAA0F;QAC1F,MAAM,OAAO,GAAG,qBAAU,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,kCAAO,IAAI,CAAC,OAAO,KAAE,WAAW,EAAE,KAAK,IAAG,CAAC;QACtF,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACrD,OAAO,CAAC,KAAK,CAAC,MAAM;iBACjB,IAAI,CAAC,eAAK,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;iBACnD,IAAI,CAAC,IAAI,kBAAkB,EAAE,CAAC;iBAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,WAAW,CAAC,KAAe;QACjC,MAAM,MAAM,GAGR,EAAE,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;QACvC,KAAK;aACF,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,yBAAa,CAAC,IAAI,CAAC,CAAC;aAChC,OAAO,CAAC,IAAI,CAAC,EAAE;YACd,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC7B;iBAAM;gBACL,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC,CAAC;QACL,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,KAA6B,EAC7B,WAA+C;QAE/C,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,GAAG,GAAG,MAAM,mBAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC1C,aAAa;YACb,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,MAAM,mBAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5D,CAAC;CACF;AApID,8CAoIC;AAED,MAAa,kBAAkB;IAI7B,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAmD;QAC/E,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO,mBACV,GAAG,kCACE,OAAO,CAAC,GAAG,GACX,aAAa,GAElB,GAAG,IACA,CAAC,MAAM;YACR,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE;YACvB,CAAC,CAAC;gBACE,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;aACtC,CAAC,CACP,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,GAAG,IAAc;QACrD,IAAI,MAAM,4BAAkB,EAAE,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACxB;QACD,qEAAqE;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAC3D,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAG,KAAe;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAEpB,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAG,KAAe;QAClC,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QACpB,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,qBAAU,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QACjF,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW;QAC9B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,qBAAU,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1F,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC5E;QACD,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;QACnE,IAAI,eAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,gBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC3B;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QACD,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACpE,IAAI,eAAU,CAAC,eAAe,CAAC,EAAE;YAC/B,gBAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC9B;IACH,CAAC;IAED,UAAU;IACF,KAAK,CAAC,SAAS,CAAC,IAAc;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACtC;QAED,0FAA0F;QAC1F,MAAM,OAAO,GAAG,qBAAU,CAAC,SAAS,EAAE,IAAI,kCAAO,IAAI,CAAC,OAAO,KAAE,WAAW,EAAE,KAAK,IAAG,CAAC;QACrF,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACrD,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC3E;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA/FD,gDA+FC;AASD,SAAgB,gBAAgB,CAC9B,WAAmB,EACnB,UAAmC,EAAE;IAErC,IAAI,cAAc,CAAC;IACnB,IAAI,OAAO,CAAC,GAAG,EAAE;QACf,cAAc,GAAG,iBAAiB,CAAC;KACpC;SAAM,IAAI,OAAO,CAAC,IAAI,EAAE;QACvB,cAAc,GAAG,kBAAkB,CAAC;KACrC;SAAM,IAAI,WAAW,CAAC,WAAW,CAAC,EAAE;QACnC,cAAc,GAAG,kBAAkB,CAAC;KACrC;SAAM;QACL,cAAc,GAAG,iBAAiB,CAAC;KACpC;IAED,OAAO,IAAI,cAAc,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5F,CAAC;AAhBD,4CAgBC;AAED,SAAgB,cAAc,CAAC,WAAmB;IAChD,MAAM,aAAa,GAAG,kCAAiB,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,wBAAwB;IAC5F,IAAI,aAAa,EAAE;QACjB,OAAO,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;KACpD;IAED,OAAO,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACnD,CAAC;AAPD,wCAOC", "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport spawnAsync, { SpawnOptions } from '@expo/spawn-async';\nimport ansiRegex from 'ansi-regex';\nimport findWorkspaceRoot from 'find-yarn-workspace-root';\nimport { existsSync } from 'fs';\nimport npmPackageArg from 'npm-package-arg';\nimport path from 'path';\nimport rimraf from 'rimraf';\nimport split from 'split';\nimport { Transform } from 'stream';\n\nimport { Logger, PackageManager } from './PackageManager';\nimport isYarnOfflineAsync from './utils/isYarnOfflineAsync';\n\n/**\n * Disable various postinstall scripts\n * - https://github.com/opencollective/opencollective-postinstall/pull/9\n */\nconst disableAdsEnv = { DISABLE_OPENCOLLECTIVE: '1', ADBLOCK: '1' };\n\nconst ansi = `(?:${ansiRegex().source})*`;\nconst npmPeerDependencyWarningPattern = new RegExp(\n  `${ansi}npm${ansi} ${ansi}WARN${ansi}.+You must install peer dependencies yourself\\\\.\\n`,\n  'g'\n);\nconst yarnPeerDependencyWarningPattern = new RegExp(\n  `${ansi}warning${ansi} \"[^\"]+\" has (?:unmet|incorrect) peer dependency \"[^\"]+\"\\\\.\\n`,\n  'g'\n);\n\n/**\n * Returns true if the project is using yarn, false if the project is using npm.\n *\n * @param projectRoot\n */\nexport function isUsingYarn(projectRoot: string): boolean {\n  const workspaceRoot = findWorkspaceRoot(projectRoot);\n  if (workspaceRoot) {\n    return existsSync(path.join(workspaceRoot, 'yarn.lock'));\n  }\n  return existsSync(path.join(projectRoot, 'yarn.lock'));\n}\n\nclass NpmStderrTransform extends Transform {\n  _transform(\n    chunk: Buffer,\n    encoding: string,\n    callback: (error?: Error | null, data?: any) => void\n  ) {\n    this.push(chunk.toString().replace(npmPeerDependencyWarningPattern, ''));\n    callback();\n  }\n}\n\nclass YarnStderrTransform extends Transform {\n  _transform(\n    chunk: Buffer,\n    encoding: string,\n    callback: (error?: Error | null, data?: any) => void\n  ) {\n    this.push(chunk.toString().replace(yarnPeerDependencyWarningPattern, ''));\n    callback();\n  }\n}\nexport class NpmPackageManager implements PackageManager {\n  options: SpawnOptions;\n\n  private log: Logger;\n\n  constructor({ cwd, log, silent }: { cwd: string; log?: Logger; silent?: boolean }) {\n    this.log = log || console.log;\n    this.options = {\n      env: {\n        ...process.env,\n        ...disableAdsEnv,\n      },\n      cwd,\n      ...(silent\n        ? { ignoreStdio: true }\n        : {\n            stdio: ['inherit', 'inherit', 'pipe'],\n          }),\n    };\n  }\n\n  get name() {\n    return 'npm';\n  }\n\n  async installAsync() {\n    await this._runAsync(['install']);\n  }\n\n  async addAsync(...names: string[]) {\n    if (!names.length) return this.installAsync();\n\n    const { versioned, unversioned } = this._parseSpecs(names);\n    if (versioned.length) {\n      await this._patchAsync(versioned, 'dependencies');\n      await this._runAsync(['install']);\n    }\n    if (unversioned.length) {\n      await this._runAsync(['install', '--save', ...unversioned.map(spec => spec.raw)]);\n    }\n  }\n\n  async addDevAsync(...names: string[]) {\n    if (!names.length) return this.installAsync();\n\n    const { versioned, unversioned } = this._parseSpecs(names);\n    if (versioned.length) {\n      await this._patchAsync(versioned, 'devDependencies');\n      await this._runAsync(['install']);\n    }\n    if (unversioned.length) {\n      await this._runAsync(['install', '--save-dev', ...unversioned.map(spec => spec.raw)]);\n    }\n  }\n\n  async versionAsync() {\n    const { stdout } = await spawnAsync('npm', ['--version'], { stdio: 'pipe' });\n    return stdout.trim();\n  }\n\n  async getConfigAsync(key: string) {\n    const { stdout } = await spawnAsync('npm', ['config', 'get', key], { stdio: 'pipe' });\n    return stdout.trim();\n  }\n\n  async removeLockfileAsync() {\n    if (!this.options.cwd) {\n      throw new Error('cwd required for NpmPackageManager.removeLockfileAsync');\n    }\n    const lockfilePath = path.join(this.options.cwd, 'package-lock.json');\n    if (existsSync(lockfilePath)) {\n      rimraf.sync(lockfilePath);\n    }\n  }\n\n  async cleanAsync() {\n    if (!this.options.cwd) {\n      throw new Error('cwd required for NpmPackageManager.cleanAsync');\n    }\n    const nodeModulesPath = path.join(this.options.cwd, 'node_modules');\n    if (existsSync(nodeModulesPath)) {\n      rimraf.sync(nodeModulesPath);\n    }\n  }\n\n  // Private\n  private async _runAsync(args: string[]) {\n    if (!this.options.ignoreStdio) {\n      this.log(`> npm ${args.join(' ')}`);\n    }\n\n    // Have spawnAsync consume stdio but we don't actually do anything with it if it's ignored\n    const promise = spawnAsync('npm', [...args], { ...this.options, ignoreStdio: false });\n    if (promise.child.stderr && !this.options.ignoreStdio) {\n      promise.child.stderr\n        .pipe(split(/\\r?\\n/, (line: string) => line + '\\n'))\n        .pipe(new NpmStderrTransform())\n        .pipe(process.stderr);\n    }\n    return promise;\n  }\n\n  private _parseSpecs(names: string[]) {\n    const result: {\n      versioned: npmPackageArg.Result[];\n      unversioned: npmPackageArg.Result[];\n    } = { versioned: [], unversioned: [] };\n    names\n      .map(name => npmPackageArg(name))\n      .forEach(spec => {\n        if (spec.rawSpec) {\n          result.versioned.push(spec);\n        } else {\n          result.unversioned.push(spec);\n        }\n      });\n    return result;\n  }\n\n  private async _patchAsync(\n    specs: npmPackageArg.Result[],\n    packageType: 'dependencies' | 'devDependencies'\n  ) {\n    const pkgPath = path.join(this.options.cwd || '.', 'package.json');\n    const pkg = await JsonFile.readAsync(pkgPath);\n    specs.forEach(spec => {\n      pkg[packageType] = pkg[packageType] || {};\n      // @ts-ignore\n      pkg[packageType][spec.name!] = spec.rawSpec;\n    });\n    await JsonFile.writeAsync(pkgPath, pkg, { json5: false });\n  }\n}\n\nexport class YarnPackageManager implements PackageManager {\n  options: SpawnOptions;\n  private log: Logger;\n\n  constructor({ cwd, log, silent }: { cwd: string; log?: Logger; silent?: boolean }) {\n    this.log = log || console.log;\n    this.options = {\n      env: {\n        ...process.env,\n        ...disableAdsEnv,\n      },\n      cwd,\n      ...(silent\n        ? { ignoreStdio: true }\n        : {\n            stdio: ['inherit', 'inherit', 'pipe'],\n          }),\n    };\n  }\n\n  get name() {\n    return 'Yarn';\n  }\n\n  private async withOfflineSupportAsync(...args: string[]): Promise<string[]> {\n    if (await isYarnOfflineAsync()) {\n      args.push('--offline');\n    }\n    // TODO: Maybe prompt about being offline and using local yarn cache.\n    return args;\n  }\n\n  async installAsync() {\n    const args = await this.withOfflineSupportAsync('install');\n    await this._runAsync(args);\n  }\n\n  async addAsync(...names: string[]) {\n    if (!names.length) return this.installAsync();\n    const args = await this.withOfflineSupportAsync('add');\n    args.push(...names);\n\n    await this._runAsync(args);\n  }\n\n  async addDevAsync(...names: string[]) {\n    if (!names.length) return this.installAsync();\n    const args = await this.withOfflineSupportAsync('add', '--dev');\n    args.push(...names);\n    await this._runAsync(args);\n  }\n\n  async versionAsync() {\n    const { stdout } = await spawnAsync('yarnpkg', ['--version'], { stdio: 'pipe' });\n    return stdout.trim();\n  }\n\n  async getConfigAsync(key: string) {\n    const { stdout } = await spawnAsync('yarnpkg', ['config', 'get', key], { stdio: 'pipe' });\n    return stdout.trim();\n  }\n\n  async removeLockfileAsync() {\n    if (!this.options.cwd) {\n      throw new Error('cwd required for YarnPackageManager.removeLockfileAsync');\n    }\n    const lockfilePath = path.join(this.options.cwd, 'yarn-lock.json');\n    if (existsSync(lockfilePath)) {\n      rimraf.sync(lockfilePath);\n    }\n  }\n\n  async cleanAsync() {\n    if (!this.options.cwd) {\n      throw new Error('cwd required for YarnPackageManager.cleanAsync');\n    }\n    const nodeModulesPath = path.join(this.options.cwd, 'node_modules');\n    if (existsSync(nodeModulesPath)) {\n      rimraf.sync(nodeModulesPath);\n    }\n  }\n\n  // Private\n  private async _runAsync(args: string[]) {\n    if (!this.options.ignoreStdio) {\n      this.log(`> yarn ${args.join(' ')}`);\n    }\n\n    // Have spawnAsync consume stdio but we don't actually do anything with it if it's ignored\n    const promise = spawnAsync('yarnpkg', args, { ...this.options, ignoreStdio: false });\n    if (promise.child.stderr && !this.options.ignoreStdio) {\n      promise.child.stderr.pipe(new YarnStderrTransform()).pipe(process.stderr);\n    }\n    return promise;\n  }\n}\n\nexport type CreateForProjectOptions = {\n  npm?: boolean;\n  yarn?: boolean;\n  log?: Logger;\n  silent?: boolean;\n};\n\nexport function createForProject(\n  projectRoot: string,\n  options: CreateForProjectOptions = {}\n): NpmPackageManager | YarnPackageManager {\n  let PackageManager;\n  if (options.npm) {\n    PackageManager = NpmPackageManager;\n  } else if (options.yarn) {\n    PackageManager = YarnPackageManager;\n  } else if (isUsingYarn(projectRoot)) {\n    PackageManager = YarnPackageManager;\n  } else {\n    PackageManager = NpmPackageManager;\n  }\n\n  return new PackageManager({ cwd: projectRoot, log: options.log, silent: options.silent });\n}\n\nexport function getModulesPath(projectRoot: string): string {\n  const workspaceRoot = findWorkspaceRoot(path.resolve(projectRoot)); // Absolute path or null\n  if (workspaceRoot) {\n    return path.resolve(workspaceRoot, 'node_modules');\n  }\n\n  return path.resolve(projectRoot, 'node_modules');\n}\n"]}