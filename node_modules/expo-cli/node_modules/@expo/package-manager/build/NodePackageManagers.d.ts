import { SpawnOptions } from '@expo/spawn-async';
import { Logger, PackageManager } from './PackageManager';
/**
 * Returns true if the project is using yarn, false if the project is using npm.
 *
 * @param projectRoot
 */
export declare function isUsingYarn(projectRoot: string): boolean;
export declare class NpmPackageManager implements PackageManager {
    options: SpawnOptions;
    private log;
    constructor({ cwd, log, silent }: {
        cwd: string;
        log?: Logger;
        silent?: boolean;
    });
    get name(): string;
    installAsync(): Promise<void>;
    addAsync(...names: string[]): Promise<void>;
    addDevAsync(...names: string[]): Promise<void>;
    versionAsync(): Promise<string>;
    getConfigAsync(key: string): Promise<string>;
    removeLockfileAsync(): Promise<void>;
    cleanAsync(): Promise<void>;
    private _runAsync;
    private _parseSpecs;
    private _patchAsync;
}
export declare class YarnPackageManager implements PackageManager {
    options: SpawnOptions;
    private log;
    constructor({ cwd, log, silent }: {
        cwd: string;
        log?: Logger;
        silent?: boolean;
    });
    get name(): string;
    private withOfflineSupportAsync;
    installAsync(): Promise<void>;
    addAsync(...names: string[]): Promise<void>;
    addDevAsync(...names: string[]): Promise<void>;
    versionAsync(): Promise<string>;
    getConfigAsync(key: string): Promise<string>;
    removeLockfileAsync(): Promise<void>;
    cleanAsync(): Promise<void>;
    private _runAsync;
}
export declare type CreateForProjectOptions = {
    npm?: boolean;
    yarn?: boolean;
    log?: Logger;
    silent?: boolean;
};
export declare function createForProject(projectRoot: string, options?: CreateForProjectOptions): NpmPackageManager | YarnPackageManager;
export declare function getModulesPath(projectRoot: string): string;
