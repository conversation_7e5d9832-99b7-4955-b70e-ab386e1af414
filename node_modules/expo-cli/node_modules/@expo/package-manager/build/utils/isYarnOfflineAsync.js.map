{"version": 3, "file": "isYarnOfflineAsync.js", "sourceRoot": "", "sources": ["../../src/utils/isYarnOfflineAsync.ts"], "names": [], "mappings": ";;;;;AAAA,iDAAyC;AACzC,8CAAsB;AACtB,8CAAsB;AAEtB,SAAgB,WAAW;;IACzB,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;QAC3B,aAAO,OAAO,CAAC,GAAG,CAAC,WAAW,mCAAI,IAAI,CAAC;KACxC;IAED,IAAI;QACF,MAAM,UAAU,GAAG,wBAAQ,CAAC,4BAA4B,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QAC5E,OAAO,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;KAClD;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAXD,kCAWC;AAED,SAAS,mBAAmB,CAAC,GAAW;IACtC,OAAO,IAAI,OAAO,CAAU,OAAO,CAAC,EAAE;QACpC,aAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YACpB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACY,KAAK,UAAU,kBAAkB;IAC9C,IAAI,MAAM,mBAAmB,CAAC,sBAAsB,CAAC,EAAE;QACrD,OAAO,KAAK,CAAC;KACd;IAED,MAAM,KAAK,GAAG,WAAW,EAAE,CAAC;IAE5B,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IAED,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtC,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CAAC,CAAC,MAAM,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC;AAjBD,qCAiBC", "sourcesContent": ["import { execSync } from 'child_process';\nimport dns from 'dns';\nimport url from 'url';\n\nexport function getNpmProxy(): string | null {\n  if (process.env.https_proxy) {\n    return process.env.https_proxy ?? null;\n  }\n\n  try {\n    const httpsProxy = execSync('npm config get https-proxy').toString().trim();\n    return httpsProxy !== 'null' ? httpsProxy : null;\n  } catch (e) {\n    return null;\n  }\n}\n\nfunction isUrlAvailableAsync(url: string): Promise<boolean> {\n  return new Promise<boolean>(resolve => {\n    dns.lookup(url, err => {\n      resolve(!err);\n    });\n  });\n}\n\n/**\n * Determine if you should use yarn offline or not.\n */\nexport default async function isYarnOfflineAsync(): Promise<boolean> {\n  if (await isUrlAvailableAsync('registry.yarnpkg.com')) {\n    return false;\n  }\n\n  const proxy = getNpmProxy();\n\n  if (!proxy) {\n    return true;\n  }\n\n  const { hostname } = url.parse(proxy);\n  if (!hostname) {\n    return true;\n  }\n\n  return !(await isUrlAvailableAsync(hostname));\n}\n"]}