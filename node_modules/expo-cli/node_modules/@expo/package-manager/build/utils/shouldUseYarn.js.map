{"version": 3, "file": "shouldUseYarn.js", "sourceRoot": "", "sources": ["../../src/utils/shouldUseYarn.ts"], "names": [], "mappings": ";;AAAA,iDAAyC;AAEzC,SAAwB,aAAa;;IACnC,IAAI;QACF,UAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,0CAAE,UAAU,CAAC,MAAM,GAAG;YACzD,OAAO,IAAI,CAAC;SACb;QACD,wBAAQ,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAVD,gCAUC", "sourcesContent": ["import { execSync } from 'child_process';\n\nexport default function shouldUseYarn(): boolean {\n  try {\n    if (process.env.npm_config_user_agent?.startsWith('yarn')) {\n      return true;\n    }\n    execSync('yarnpkg --version', { stdio: 'ignore' });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n"]}