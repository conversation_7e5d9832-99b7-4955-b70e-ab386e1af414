"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const spawn_async_1 = __importDefault(require("@expo/spawn-async"));
const chalk_1 = __importDefault(require("chalk"));
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const PackageManager_1 = require("./PackageManager");
class CocoaPodsPackageManager {
    constructor({ cwd, log, silent }) {
        this.log = log || console.log;
        this.silent = !!silent;
        this.options = Object.assign({ cwd }, (silent
            ? { ignoreStdio: true }
            : {
                stdio: ['inherit', 'inherit', 'pipe'],
            }));
    }
    static getPodProjectRoot(projectRoot) {
        if (CocoaPodsPackageManager.isUsingPods(projectRoot))
            return projectRoot;
        const iosProject = path_1.default.join(projectRoot, 'ios');
        if (CocoaPodsPackageManager.isUsingPods(iosProject))
            return iosProject;
        const macOsProject = path_1.default.join(projectRoot, 'macos');
        if (CocoaPodsPackageManager.isUsingPods(macOsProject))
            return macOsProject;
        return null;
    }
    static isUsingPods(projectRoot) {
        return fs_1.existsSync(path_1.default.join(projectRoot, 'Podfile'));
    }
    static async gemInstallCLIAsync(nonInteractive = false, spawnOptions = { stdio: 'inherit' }) {
        const options = ['install', 'cocoapods', '--no-document'];
        try {
            // In case the user has run sudo before running the command we can properly install CocoaPods without prompting for an interaction.
            await spawn_async_1.default('gem', options, spawnOptions);
        }
        catch (error) {
            if (nonInteractive) {
                throw error;
            }
            // If the user doesn't have permission then we can prompt them to use sudo.
            await PackageManager_1.spawnSudoAsync(['gem', ...options], spawnOptions);
        }
    }
    static async brewLinkCLIAsync(spawnOptions = { stdio: 'inherit' }) {
        await spawn_async_1.default('brew', ['link', 'cocoapods'], spawnOptions);
    }
    static async brewInstallCLIAsync(spawnOptions = { stdio: 'inherit' }) {
        await spawn_async_1.default('brew', ['install', 'cocoapods'], spawnOptions);
    }
    static async installCLIAsync({ nonInteractive = false, spawnOptions = { stdio: 'inherit' }, }) {
        var _a;
        if (!spawnOptions) {
            spawnOptions = { stdio: 'inherit' };
        }
        const silent = !!spawnOptions.ignoreStdio;
        try {
            !silent && console.log(chalk_1.default.magenta(`\u203A Attempting to install CocoaPods with Gem`));
            await CocoaPodsPackageManager.gemInstallCLIAsync(nonInteractive, spawnOptions);
            !silent && console.log(chalk_1.default.magenta(`\u203A Successfully installed CocoaPods with Gem`));
            return true;
        }
        catch (error) {
            if (!silent) {
                console.log(chalk_1.default.yellow(`\u203A Failed to install CocoaPods with Gem`));
                console.log(chalk_1.default.red((_a = error.stderr) !== null && _a !== void 0 ? _a : error.message));
                console.log(chalk_1.default.magenta(`\u203A Attempting to install CocoaPods with Homebrew`));
            }
            try {
                await CocoaPodsPackageManager.brewInstallCLIAsync(spawnOptions);
                if (!(await CocoaPodsPackageManager.isCLIInstalledAsync(spawnOptions))) {
                    try {
                        await CocoaPodsPackageManager.brewLinkCLIAsync(spawnOptions);
                        // Still not available after linking? Bail out
                        if (!(await CocoaPodsPackageManager.isCLIInstalledAsync(spawnOptions))) {
                            throw new Error();
                        }
                    }
                    catch (e) {
                        throw Error('Homebrew installation appeared to succeed but CocoaPods not found in PATH and unable to link.');
                    }
                }
                !silent &&
                    console.log(chalk_1.default.magenta(`\u203A Successfully installed CocoaPods with Homebrew`));
                return true;
            }
            catch (error) {
                !silent &&
                    console.log(chalk_1.default.yellow(`\u203A Failed to install CocoaPods with Homebrew. Please install CocoaPods manually and try again.`));
                throw new Error(error.stderr);
            }
        }
    }
    static isAvailable(projectRoot, silent) {
        if (process.platform !== 'darwin') {
            !silent && console.log(chalk_1.default.red('CocoaPods is only supported on macOS machines'));
            return false;
        }
        if (!CocoaPodsPackageManager.isUsingPods(projectRoot)) {
            !silent && console.log(chalk_1.default.yellow('CocoaPods is not supported in this project'));
            return false;
        }
        return true;
    }
    static async isCLIInstalledAsync(spawnOptions = { stdio: 'inherit' }) {
        try {
            await spawn_async_1.default('pod', ['--version'], spawnOptions);
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    get name() {
        return 'CocoaPods';
    }
    async installAsync() {
        await this._installAsync();
    }
    isCLIInstalledAsync() {
        return CocoaPodsPackageManager.isCLIInstalledAsync(this.options);
    }
    installCLIAsync() {
        return CocoaPodsPackageManager.installCLIAsync({
            nonInteractive: true,
            spawnOptions: this.options,
        });
    }
    async _installAsync(shouldUpdate = true) {
        var _a;
        try {
            await this._runAsync(['install']);
        }
        catch (error) {
            const stderr = (_a = error.stderr) !== null && _a !== void 0 ? _a : error.stdout;
            // When pods are outdated, they'll throw an error informing you to run "pod repo update"
            // Attempt to run that command and try installing again.
            if (stderr.includes('pod repo update') && shouldUpdate) {
                !this.silent &&
                    console.log(chalk_1.default.yellow(`\u203A Couldn't install Pods. ${chalk_1.default.dim(`Updating the repo and trying again.`)}`));
                await this.podRepoUpdateAsync();
                // Include a boolean to ensure pod repo update isn't invoked in the unlikely case where the pods fail to update.
                await this._installAsync(false);
            }
            else {
                throw new Error(stderr);
            }
        }
    }
    async addAsync(...names) {
        throw new Error('Unimplemented');
    }
    async addDevAsync(...names) {
        throw new Error('Unimplemented');
    }
    async versionAsync() {
        const { stdout } = await spawn_async_1.default('pod', ['--version'], this.options);
        return stdout.trim();
    }
    async getConfigAsync(key) {
        throw new Error('Unimplemented');
    }
    async removeLockfileAsync() {
        throw new Error('Unimplemented');
    }
    async cleanAsync() {
        throw new Error('Unimplemented');
    }
    // Private
    async podRepoUpdateAsync() {
        var _a;
        try {
            await this._runAsync(['repo', 'update']);
        }
        catch (error) {
            throw new Error((_a = error.stderr) !== null && _a !== void 0 ? _a : error.stdout);
        }
    }
    async _runAsync(args) {
        if (!this.silent) {
            this.log(`> pod ${args.join(' ')}`);
        }
        return spawn_async_1.default('pod', [...args], this.options);
    }
}
exports.CocoaPodsPackageManager = CocoaPodsPackageManager;
//# sourceMappingURL=CocoaPodsPackageManager.js.map