import { SpawnOptions } from '@expo/spawn-async';
import { Logger, PackageManager } from './PackageManager';
export declare class CocoaPodsPackageManager implements PackageManager {
    options: SpawnOptions;
    private log;
    private silent;
    static getPodProjectRoot(projectRoot: string): string | null;
    static isUsingPods(projectRoot: string): boolean;
    static gemInstallCLIAsync(nonInteractive?: boolean, spawnOptions?: SpawnOptions): Promise<void>;
    static brewLinkCLIAsync(spawnOptions?: SpawnOptions): Promise<void>;
    static brewInstallCLIAsync(spawnOptions?: SpawnOptions): Promise<void>;
    static installCLIAsync({ nonInteractive, spawnOptions, }: {
        nonInteractive?: boolean;
        spawnOptions?: SpawnOptions;
    }): Promise<boolean>;
    static isAvailable(projectRoot: string, silent: boolean): boolean;
    static isCLIInstalledAsync(spawnOptions?: SpawnOptions): Promise<boolean>;
    constructor({ cwd, log, silent }: {
        cwd: string;
        log?: Logger;
        silent?: boolean;
    });
    get name(): string;
    installAsync(): Promise<void>;
    isCLIInstalledAsync(): Promise<boolean>;
    installCLIAsync(): Promise<boolean>;
    private _installAsync;
    addAsync(...names: string[]): Promise<void>;
    addDevAsync(...names: string[]): Promise<void>;
    versionAsync(): Promise<string>;
    getConfigAsync(key: string): Promise<string>;
    removeLockfileAsync(): Promise<void>;
    cleanAsync(): Promise<void>;
    private podRepoUpdateAsync;
    private _runAsync;
}
