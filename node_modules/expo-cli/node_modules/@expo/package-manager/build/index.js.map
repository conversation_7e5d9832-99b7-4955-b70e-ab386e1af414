{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;AAAA,sCAAiC;AACjC,2CAAsC;AACtC,+CAA0C;AAC1C,uDAAiE;AAAxD,wCAAA,OAAO,CAAiB;AACjC,iEAA2E;AAAlE,kDAAA,OAAO,CAAsB", "sourcesContent": ["export * from './PackageManager';\nexport * from './NodePackageManagers';\nexport * from './CocoaPodsPackageManager';\nexport { default as shouldUseYarn } from './utils/shouldUseYarn';\nexport { default as isYarnOfflineAsync } from './utils/isYarnOfflineAsync';\n"]}