{"version": 3, "file": "PackageManager.js", "sourceRoot": "", "sources": ["../src/PackageManager.ts"], "names": [], "mappings": ";;;;;AAAA,oEAA6D;AAC7D,2BAAkC;AAClC,8DAA+B;AAc/B,SAAgB,sBAAsB;IACpC,OAAO,iBAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AACrC,CAAC;AAFD,wDAEC;AAEM,KAAK,UAAU,cAAc,CAAC,OAAiB,EAAE,YAA0B;IAChF,oDAAoD;IACpD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,qBAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,KAAK,CAAC,EAAE;gBAC5D,IAAI,KAAK,EAAE;oBACT,MAAM,CAAC,KAAK,CAAC,CAAC;iBACf;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,2DAA2D;QAC3D,iFAAiF;QACjF,OAAO,CAAC,GAAG,CACT,8HAA8H,CAC/H,CAAC;QACF,MAAM,qBAAU,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;KACjD;AACH,CAAC;AAnBD,wCAmBC", "sourcesContent": ["import spawnAsync, { SpawnOptions } from '@expo/spawn-async';\nimport { realpathSync } from 'fs';\nimport sudo from 'sudo-prompt';\n\nexport type Logger = (...args: any[]) => void;\n\nexport interface PackageManager {\n  installAsync(): Promise<void>;\n  addAsync(...names: string[]): Promise<void>;\n  addDevAsync(...names: string[]): Promise<void>;\n  versionAsync(): Promise<string>;\n  getConfigAsync(key: string): Promise<string>;\n  removeLockfileAsync(): Promise<void>;\n  cleanAsync(): Promise<void>;\n}\n\nexport function getPossibleProjectRoot(): string {\n  return realpathSync(process.cwd());\n}\n\nexport async function spawnSudoAsync(command: string[], spawnOptions: SpawnOptions): Promise<void> {\n  // sudo prompt only seems to work on win32 machines.\n  if (process.platform === 'win32') {\n    return new Promise((resolve, reject) => {\n      sudo.exec(command.join(' '), { name: 'pod install' }, error => {\n        if (error) {\n          reject(error);\n        }\n        resolve();\n      });\n    });\n  } else {\n    // Attempt to use sudo to run the command on Mac and Linux.\n    // TODO(Bacon): Make a v of sudo-prompt that's win32 only for better bundle size.\n    console.log(\n      'Your password might be needed to install CocoaPods CLI: https://guides.cocoapods.org/using/getting-started.html#installation'\n    );\n    await spawnAsync('sudo', command, spawnOptions);\n  }\n}\n"]}