{"version": 3, "file": "CocoaPodsPackageManager.js", "sourceRoot": "", "sources": ["../src/CocoaPodsPackageManager.ts"], "names": [], "mappings": ";;;;;AAAA,oEAA0E;AAC1E,kDAA0B;AAC1B,2BAAgC;AAChC,gDAAwB;AAExB,qDAA0E;AAE1E,MAAa,uBAAuB;IA2HlC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAmD;QAC/E,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;QACvB,IAAI,CAAC,OAAO,mBACV,GAAG,IACA,CAAC,MAAM;YACR,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE;YACvB,CAAC,CAAC;gBACE,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;aACtC,CAAC,CACP,CAAC;IACJ,CAAC;IAjID,MAAM,CAAC,iBAAiB,CAAC,WAAmB;QAC1C,IAAI,uBAAuB,CAAC,WAAW,CAAC,WAAW,CAAC;YAAE,OAAO,WAAW,CAAC;QACzE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,uBAAuB,CAAC,WAAW,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACvE,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACrD,IAAI,uBAAuB,CAAC,WAAW,CAAC,YAAY,CAAC;YAAE,OAAO,YAAY,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,WAAmB;QACpC,OAAO,eAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,iBAA0B,KAAK,EAC/B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,MAAM,OAAO,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QAE1D,IAAI;YACF,mIAAmI;YACnI,MAAM,qBAAU,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SAChD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,cAAc,EAAE;gBAClB,MAAM,KAAK,CAAC;aACb;YACD,2EAA2E;YAC3E,MAAM,+BAAc,CAAC,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC;SACzD;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAC7E,MAAM,qBAAU,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,MAAM,qBAAU,CAAC,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,EAC3B,cAAc,GAAG,KAAK,EACtB,YAAY,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,GAIpC;;QACC,IAAI,CAAC,YAAY,EAAE;YACjB,YAAY,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;SACrC;QACD,MAAM,MAAM,GAAG,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC;QAE1C,IAAI;YACF,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC,CAAC;YACzF,MAAM,uBAAuB,CAAC,kBAAkB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC/E,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC,CAAC;YAC1F,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC,CAAC;gBACzE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,OAAC,KAAK,CAAC,MAAM,mCAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC,CAAC;aACpF;YACD,IAAI;gBACF,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;gBAChE,IAAI,CAAC,CAAC,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,EAAE;oBACtE,IAAI;wBACF,MAAM,uBAAuB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;wBAC7D,8CAA8C;wBAC9C,IAAI,CAAC,CAAC,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,EAAE;4BACtE,MAAM,IAAI,KAAK,EAAE,CAAC;yBACnB;qBACF;oBAAC,OAAO,CAAC,EAAE;wBACV,MAAM,KAAK,CACT,+FAA+F,CAChG,CAAC;qBACH;iBACF;gBAED,CAAC,MAAM;oBACL,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC,CAAC;gBACtF,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,KAAK,EAAE;gBACd,CAAC,MAAM;oBACL,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,MAAM,CACV,oGAAoG,CACrG,CACF,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;aAC/B;SACF;IACH,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,WAAmB,EAAE,MAAe;QACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACjC,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC,CAAC;YACnF,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;YACrD,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC,CAAC;YACnF,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,IAAI;YACF,MAAM,qBAAU,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;SACb;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAeD,IAAI,IAAI;QACN,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;IAC7B,CAAC;IAEM,mBAAmB;QACxB,OAAO,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnE,CAAC;IAEM,eAAe;QACpB,OAAO,uBAAuB,CAAC,eAAe,CAAC;YAC7C,cAAc,EAAE,IAAI;YACpB,YAAY,EAAE,IAAI,CAAC,OAAO;SAC3B,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,eAAwB,IAAI;;QACtD,IAAI;YACF,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;SACnC;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,MAAM,SAAG,KAAK,CAAC,MAAM,mCAAI,KAAK,CAAC,MAAM,CAAC;YAE5C,wFAAwF;YACxF,wDAAwD;YACxD,IAAI,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,YAAY,EAAE;gBACtD,CAAC,IAAI,CAAC,MAAM;oBACV,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,MAAM,CACV,iCAAiC,eAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,EAAE,CACpF,CACF,CAAC;gBACJ,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChC,gHAAgH;gBAChH,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;aACzB;SACF;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAG,KAAe;QAC/B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAG,KAAe;QAClC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,qBAAU,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW;QAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,UAAU;IACF,KAAK,CAAC,kBAAkB;;QAC9B,IAAI;YACF,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC1C;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,OAAC,KAAK,CAAC,MAAM,mCAAI,KAAK,CAAC,MAAM,CAAC,CAAC;SAC/C;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAc;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACrC;QACD,OAAO,qBAAU,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;CACF;AA3ND,0DA2NC", "sourcesContent": ["import spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { existsSync } from 'fs';\nimport path from 'path';\n\nimport { Logger, PackageManager, spawnSudoAsync } from './PackageManager';\n\nexport class CocoaPodsPackageManager implements PackageManager {\n  options: SpawnOptions;\n  private log: Logger;\n  private silent: boolean;\n\n  static getPodProjectRoot(projectRoot: string): string | null {\n    if (CocoaPodsPackageManager.isUsingPods(projectRoot)) return projectRoot;\n    const iosProject = path.join(projectRoot, 'ios');\n    if (CocoaPodsPackageManager.isUsingPods(iosProject)) return iosProject;\n    const macOsProject = path.join(projectRoot, 'macos');\n    if (CocoaPodsPackageManager.isUsingPods(macOsProject)) return macOsProject;\n    return null;\n  }\n\n  static isUsingPods(projectRoot: string): boolean {\n    return existsSync(path.join(projectRoot, 'Podfile'));\n  }\n\n  static async gemInstallCLIAsync(\n    nonInteractive: boolean = false,\n    spawnOptions: SpawnOptions = { stdio: 'inherit' }\n  ): Promise<void> {\n    const options = ['install', 'cocoapods', '--no-document'];\n\n    try {\n      // In case the user has run sudo before running the command we can properly install CocoaPods without prompting for an interaction.\n      await spawnAsync('gem', options, spawnOptions);\n    } catch (error) {\n      if (nonInteractive) {\n        throw error;\n      }\n      // If the user doesn't have permission then we can prompt them to use sudo.\n      await spawnSudoAsync(['gem', ...options], spawnOptions);\n    }\n  }\n\n  static async brewLinkCLIAsync(spawnOptions: SpawnOptions = { stdio: 'inherit' }): Promise<void> {\n    await spawnAsync('brew', ['link', 'cocoapods'], spawnOptions);\n  }\n\n  static async brewInstallCLIAsync(\n    spawnOptions: SpawnOptions = { stdio: 'inherit' }\n  ): Promise<void> {\n    await spawnAsync('brew', ['install', 'cocoapods'], spawnOptions);\n  }\n\n  static async installCLIAsync({\n    nonInteractive = false,\n    spawnOptions = { stdio: 'inherit' },\n  }: {\n    nonInteractive?: boolean;\n    spawnOptions?: SpawnOptions;\n  }): Promise<boolean> {\n    if (!spawnOptions) {\n      spawnOptions = { stdio: 'inherit' };\n    }\n    const silent = !!spawnOptions.ignoreStdio;\n\n    try {\n      !silent && console.log(chalk.magenta(`\\u203A Attempting to install CocoaPods with Gem`));\n      await CocoaPodsPackageManager.gemInstallCLIAsync(nonInteractive, spawnOptions);\n      !silent && console.log(chalk.magenta(`\\u203A Successfully installed CocoaPods with Gem`));\n      return true;\n    } catch (error) {\n      if (!silent) {\n        console.log(chalk.yellow(`\\u203A Failed to install CocoaPods with Gem`));\n        console.log(chalk.red(error.stderr ?? error.message));\n        console.log(chalk.magenta(`\\u203A Attempting to install CocoaPods with Homebrew`));\n      }\n      try {\n        await CocoaPodsPackageManager.brewInstallCLIAsync(spawnOptions);\n        if (!(await CocoaPodsPackageManager.isCLIInstalledAsync(spawnOptions))) {\n          try {\n            await CocoaPodsPackageManager.brewLinkCLIAsync(spawnOptions);\n            // Still not available after linking? Bail out\n            if (!(await CocoaPodsPackageManager.isCLIInstalledAsync(spawnOptions))) {\n              throw new Error();\n            }\n          } catch (e) {\n            throw Error(\n              'Homebrew installation appeared to succeed but CocoaPods not found in PATH and unable to link.'\n            );\n          }\n        }\n\n        !silent &&\n          console.log(chalk.magenta(`\\u203A Successfully installed CocoaPods with Homebrew`));\n        return true;\n      } catch (error) {\n        !silent &&\n          console.log(\n            chalk.yellow(\n              `\\u203A Failed to install CocoaPods with Homebrew. Please install CocoaPods manually and try again.`\n            )\n          );\n        throw new Error(error.stderr);\n      }\n    }\n  }\n\n  static isAvailable(projectRoot: string, silent: boolean): boolean {\n    if (process.platform !== 'darwin') {\n      !silent && console.log(chalk.red('CocoaPods is only supported on macOS machines'));\n      return false;\n    }\n    if (!CocoaPodsPackageManager.isUsingPods(projectRoot)) {\n      !silent && console.log(chalk.yellow('CocoaPods is not supported in this project'));\n      return false;\n    }\n    return true;\n  }\n\n  static async isCLIInstalledAsync(\n    spawnOptions: SpawnOptions = { stdio: 'inherit' }\n  ): Promise<boolean> {\n    try {\n      await spawnAsync('pod', ['--version'], spawnOptions);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  constructor({ cwd, log, silent }: { cwd: string; log?: Logger; silent?: boolean }) {\n    this.log = log || console.log;\n    this.silent = !!silent;\n    this.options = {\n      cwd,\n      ...(silent\n        ? { ignoreStdio: true }\n        : {\n            stdio: ['inherit', 'inherit', 'pipe'],\n          }),\n    };\n  }\n\n  get name() {\n    return 'CocoaPods';\n  }\n\n  async installAsync() {\n    await this._installAsync();\n  }\n\n  public isCLIInstalledAsync() {\n    return CocoaPodsPackageManager.isCLIInstalledAsync(this.options);\n  }\n\n  public installCLIAsync() {\n    return CocoaPodsPackageManager.installCLIAsync({\n      nonInteractive: true,\n      spawnOptions: this.options,\n    });\n  }\n\n  private async _installAsync(shouldUpdate: boolean = true): Promise<void> {\n    try {\n      await this._runAsync(['install']);\n    } catch (error) {\n      const stderr = error.stderr ?? error.stdout;\n\n      // When pods are outdated, they'll throw an error informing you to run \"pod repo update\"\n      // Attempt to run that command and try installing again.\n      if (stderr.includes('pod repo update') && shouldUpdate) {\n        !this.silent &&\n          console.log(\n            chalk.yellow(\n              `\\u203A Couldn't install Pods. ${chalk.dim(`Updating the repo and trying again.`)}`\n            )\n          );\n        await this.podRepoUpdateAsync();\n        // Include a boolean to ensure pod repo update isn't invoked in the unlikely case where the pods fail to update.\n        await this._installAsync(false);\n      } else {\n        throw new Error(stderr);\n      }\n    }\n  }\n\n  async addAsync(...names: string[]) {\n    throw new Error('Unimplemented');\n  }\n\n  async addDevAsync(...names: string[]) {\n    throw new Error('Unimplemented');\n  }\n\n  async versionAsync() {\n    const { stdout } = await spawnAsync('pod', ['--version'], this.options);\n    return stdout.trim();\n  }\n\n  async getConfigAsync(key: string): Promise<string> {\n    throw new Error('Unimplemented');\n  }\n\n  async removeLockfileAsync() {\n    throw new Error('Unimplemented');\n  }\n\n  async cleanAsync() {\n    throw new Error('Unimplemented');\n  }\n\n  // Private\n  private async podRepoUpdateAsync(): Promise<void> {\n    try {\n      await this._runAsync(['repo', 'update']);\n    } catch (error) {\n      throw new Error(error.stderr ?? error.stdout);\n    }\n  }\n\n  private async _runAsync(args: string[]): Promise<SpawnResult> {\n    if (!this.silent) {\n      this.log(`> pod ${args.join(' ')}`);\n    }\n    return spawnAsync('pod', [...args], this.options);\n  }\n}\n"]}