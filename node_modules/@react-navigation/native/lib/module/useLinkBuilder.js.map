{"version": 3, "names": ["getPathFromState", "NavigationHelpersContext", "React", "LinkingContext", "getRootStateForNavigate", "navigation", "state", "parent", "getParent", "parentState", "getState", "index", "routes", "useLinkBuilder", "useContext", "linking", "buildLink", "useCallback", "name", "params", "options", "enabled", "undefined", "path", "config"], "sourceRoot": "../../src", "sources": ["useLinkBuilder.tsx"], "mappings": "AAAA,SACEA,gBAAgB,EAEhBC,wBAAwB,QAGnB,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc,MAAM,kBAAkB;AAW7C,MAAMC,uBAAuB,GAAG,CAC9BC,UAA4B,EAC5BC,KAAmB,KACF;EACjB,MAAMC,MAAM,GAAGF,UAAU,CAACG,SAAS,EAAE;EAErC,IAAID,MAAM,EAAE;IACV,MAAME,WAAW,GAAGF,MAAM,CAACG,QAAQ,EAAE;IAErC,OAAON,uBAAuB,CAACG,MAAM,EAAE;MACrCI,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CACN;QACE,GAAGH,WAAW,CAACG,MAAM,CAACH,WAAW,CAACE,KAAK,CAAC;QACxCL,KAAK,EAAEA;MACT,CAAC;IAEL,CAAC,CAAC;EACJ;EAEA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,SAASO,cAAc,GAAG;EACvC,MAAMR,UAAU,GAAGH,KAAK,CAACY,UAAU,CAACb,wBAAwB,CAAC;EAC7D,MAAMc,OAAO,GAAGb,KAAK,CAACY,UAAU,CAACX,cAAc,CAAC;EAEhD,MAAMa,SAAS,GAAGd,KAAK,CAACe,WAAW,CACjC,CAACC,IAAY,EAAEC,MAAe,KAAK;IACjC,MAAM;MAAEC;IAAQ,CAAC,GAAGL,OAAO;IAE3B,IAAI,CAAAK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,OAAO,MAAK,KAAK,EAAE;MAC9B,OAAOC,SAAS;IAClB;IAEA,MAAMhB,KAAK,GAAGD,UAAU,GACpBD,uBAAuB,CAACC,UAAU,EAAE;MAClCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAEM,IAAI;QAAEC;MAAO,CAAC;IAC3B,CAAC,CAAC;IACF;IACA;IACA;MACER,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAEM,IAAI;QAAEC;MAAO,CAAC;IAC3B,CAAC;IAEL,MAAMI,IAAI,GAAGH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEpB,gBAAgB,GAClCoB,OAAO,CAACpB,gBAAgB,CAACM,KAAK,EAAEc,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,MAAM,CAAC,GAChDxB,gBAAgB,CAACM,KAAK,EAAEc,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,MAAM,CAAC;IAE5C,OAAOD,IAAI;EACb,CAAC,EACD,CAACR,OAAO,EAAEV,UAAU,CAAC,CACtB;EAED,OAAOW,SAAS;AAClB"}