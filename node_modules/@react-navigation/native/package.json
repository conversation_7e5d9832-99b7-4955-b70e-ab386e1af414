{"name": "@react-navigation/native", "description": "React Native integration for React Navigation", "version": "6.1.18", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "lib/commonjs/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "module": "lib/module/index.js", "types": "lib/typescript/src/index.d.ts", "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/core": "^6.4.17", "escape-string-regexp": "^4.0.0", "fast-deep-equal": "^3.1.3", "nanoid": "^3.1.23"}, "devDependencies": {"@testing-library/react-native": "^11.5.0", "@types/react": "~18.0.27", "@types/react-dom": "~18.0.10", "@types/react-native": "~0.71.3", "del-cli": "^5.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.71.8", "react-native-builder-bob": "^0.20.4", "typescript": "^4.9.4"}, "peerDependencies": {"react": "*", "react-native": "*"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "a2269aa24bc0c2ceec227dbdcc4fa4c38902396a"}