{"version": 3, "names": ["useBottomTabBarHeight", "height", "React", "useContext", "BottomTabBarHeightContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useBottomTabBarHeight.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAAoE;AAAA;AAAA;AAErD,SAASA,qBAAqB,GAAG;EAC9C,MAAMC,MAAM,GAAGC,KAAK,CAACC,UAAU,CAACC,kCAAyB,CAAC;EAE1D,IAAIH,MAAM,KAAKI,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,2FAA2F,CAC5F;EACH;EAEA,OAAOL,MAAM;AACf"}