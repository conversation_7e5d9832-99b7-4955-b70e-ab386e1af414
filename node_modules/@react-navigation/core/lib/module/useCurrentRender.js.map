{"version": 3, "names": ["React", "CurrentRenderContext", "useCurrentRender", "state", "navigation", "descriptors", "current", "useContext", "isFocused", "options", "routes", "index", "key"], "sourceRoot": "../../src", "sources": ["useCurrentRender.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB,MAAM,wBAAwB;AAiBzD;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgB,OAI5B;EAAA,IAJ6B;IACvCC,KAAK;IACLC,UAAU;IACVC;EACO,CAAC;EACR,MAAMC,OAAO,GAAGN,KAAK,CAACO,UAAU,CAACN,oBAAoB,CAAC;EAEtD,IAAIK,OAAO,IAAIF,UAAU,CAACI,SAAS,EAAE,EAAE;IACrCF,OAAO,CAACG,OAAO,GAAGJ,WAAW,CAACF,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAACC,GAAG,CAAC,CAACH,OAAO;EACtE;AACF"}