{"version": 3, "names": ["checkSerializableWithoutCircularReference", "o", "seen", "location", "undefined", "serializable", "Object", "prototype", "toString", "call", "Array", "isArray", "reason", "String", "has", "add", "i", "length", "childResult", "Set", "key", "checkSerializable"], "sourceRoot": "../../src", "sources": ["checkSerializable.tsx"], "mappings": "AAAA,MAAMA,yCAAyC,GAAG,CAChDC,CAAyB,EACzBC,IAAc,EACdC,QAA6B,KAOtB;EACP,IACEF,CAAC,KAAKG,SAAS,IACfH,CAAC,KAAK,IAAI,IACV,OAAOA,CAAC,KAAK,SAAS,IACtB,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,KAAK,QAAQ,EACrB;IACA,OAAO;MAAEI,YAAY,EAAE;IAAK,CAAC;EAC/B;EAEA,IACEC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,CAAC,CAAC,KAAK,iBAAiB,IACvD,CAACS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EACjB;IACA,OAAO;MACLI,YAAY,EAAE,KAAK;MACnBF,QAAQ;MACRS,MAAM,EAAE,OAAOX,CAAC,KAAK,UAAU,GAAG,UAAU,GAAGY,MAAM,CAACZ,CAAC;IACzD,CAAC;EACH;EAEA,IAAIC,IAAI,CAACY,GAAG,CAACb,CAAC,CAAC,EAAE;IACf,OAAO;MACLI,YAAY,EAAE,KAAK;MACnBO,MAAM,EAAE,oBAAoB;MAC5BT;IACF,CAAC;EACH;EAEAD,IAAI,CAACa,GAAG,CAACd,CAAC,CAAC;EAEX,IAAIS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EAAE;IACpB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,CAAC,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;MACjC,MAAME,WAAW,GAAGlB,yCAAyC,CAC3DC,CAAC,CAACe,CAAC,CAAC,EACJ,IAAIG,GAAG,CAAMjB,IAAI,CAAC,EAClB,CAAC,GAAGC,QAAQ,EAAEa,CAAC,CAAC,CACjB;MAED,IAAI,CAACE,WAAW,CAACb,YAAY,EAAE;QAC7B,OAAOa,WAAW;MACpB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAME,GAAG,IAAInB,CAAC,EAAE;MACnB,MAAMiB,WAAW,GAAGlB,yCAAyC,CAC3DC,CAAC,CAACmB,GAAG,CAAC,EACN,IAAID,GAAG,CAAMjB,IAAI,CAAC,EAClB,CAAC,GAAGC,QAAQ,EAAEiB,GAAG,CAAC,CACnB;MAED,IAAI,CAACF,WAAW,CAACb,YAAY,EAAE;QAC7B,OAAOa,WAAW;MACpB;IACF;EACF;EAEA,OAAO;IAAEb,YAAY,EAAE;EAAK,CAAC;AAC/B,CAAC;AAED,eAAe,SAASgB,iBAAiB,CAACpB,CAAyB,EAAE;EACnE,OAAOD,yCAAyC,CAACC,CAAC,EAAE,IAAIkB,GAAG,EAAO,EAAE,EAAE,CAAC;AACzE"}