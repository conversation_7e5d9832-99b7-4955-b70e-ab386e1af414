{"version": 3, "names": ["React", "useState", "useNavigation", "useIsFocused", "navigation", "isFocused", "setIsFocused", "valueToReturn", "useEffect", "unsubscribeFocus", "addListener", "unsubscribeBlur", "useDebugValue"], "sourceRoot": "../../src", "sources": ["useIsFocused.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAEhC,OAAOC,aAAa,MAAM,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAY,GAAY;EAC9C,MAAMC,UAAU,GAAGF,aAAa,EAAE;EAClC,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGL,QAAQ,CAACG,UAAU,CAACC,SAAS,CAAC;EAEhE,MAAME,aAAa,GAAGH,UAAU,CAACC,SAAS,EAAE;EAE5C,IAAIA,SAAS,KAAKE,aAAa,EAAE;IAC/B;IACA;IACA;IACA;IACA;IACAD,YAAY,CAACC,aAAa,CAAC;EAC7B;EAEAP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAMC,gBAAgB,GAAGL,UAAU,CAACM,WAAW,CAAC,OAAO,EAAE,MACvDJ,YAAY,CAAC,IAAI,CAAC,CACnB;IAED,MAAMK,eAAe,GAAGP,UAAU,CAACM,WAAW,CAAC,MAAM,EAAE,MACrDJ,YAAY,CAAC,KAAK,CAAC,CACpB;IAED,OAAO,MAAM;MACXG,gBAAgB,EAAE;MAClBE,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhBJ,KAAK,CAACY,aAAa,CAACL,aAAa,CAAC;EAElC,OAAOA,aAAa;AACtB"}