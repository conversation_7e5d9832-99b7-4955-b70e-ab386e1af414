{"version": 3, "file": "HTML.js", "sourceRoot": "", "sources": ["../src/HTML.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;GAOG;AACH,SAAgB,qBAAqB,CACnC,aAKC,EACD,QAAiB,KAAK;IAEtB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,EAAE,CAAC;SAC3D,MAAM,CAAC,UAAU,aAAa;QAC7B,OAAO,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,KAAK,CAAC;IAC3D,CAAC,CAAC;SACD,GAAG,CAAC,UAAU,aAAa;QAC1B,IAAI,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACpD,OAAO,KAAK,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;SAC3E;QACD,OAAO,aAAa,GAAG,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IAC9E,CAAC,CAAC,CAAC;IACL,OAAO,CACL,GAAG;QACH,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACpD,CAAC,aAAa,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,GAAG;QACH,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE,CAAC;QAC/B,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,CAClE,CAAC;AACJ,CAAC;AA3BD,sDA2BC", "sourcesContent": ["/**\n * Turn a tag definition into a html string\n * @param {HtmlTagObject} tagDefinition\n *  A tag element according to the htmlWebpackPlugin object notation\n *\n * @param xhtml {boolean}\n *   Wether the generated html should add closing slashes to be xhtml compliant\n */\nexport function htmlTagObjectToString(\n  tagDefinition: {\n    tagName: string;\n    voidTag?: boolean;\n    innerHTML?: string;\n    attributes: Record<string, string | undefined | boolean>;\n  },\n  xhtml: boolean = false\n): string {\n  const attributes = Object.keys(tagDefinition.attributes || {})\n    .filter(function (attributeName) {\n      return tagDefinition.attributes[attributeName] !== false;\n    })\n    .map(function (attributeName) {\n      if (tagDefinition.attributes[attributeName] === true) {\n        return xhtml ? attributeName + '=\"' + attributeName + '\"' : attributeName;\n      }\n      return attributeName + '=\"' + tagDefinition.attributes[attributeName] + '\"';\n    });\n  return (\n    '<' +\n    [tagDefinition.tagName].concat(attributes).join(' ') +\n    (tagDefinition.voidTag && xhtml ? '/' : '') +\n    '>' +\n    (tagDefinition.innerHTML || '') +\n    (tagDefinition.voidTag ? '' : '</' + tagDefinition.tagName + '>')\n  );\n}\n"]}