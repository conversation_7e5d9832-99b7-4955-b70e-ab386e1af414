{"version": 3, "file": "Orientation.js", "sourceRoot": "", "sources": ["../src/Orientation.ts"], "names": [], "mappings": ";;;AAWA,oEAAoE;AACpE,MAAM,gBAAgB,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAEpD,MAAM,qBAAqB,GAAG,CAAC,UAAU,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;AAErF,MAAM,sBAAsB,GAAG,CAAC,WAAW,EAAE,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;AAEzF,SAAgB,OAAO,CAAC,WAAmB;IACzC,OAAO,CACL,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC;QACtC,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC5C,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC5C,CAAC;AACJ,CAAC;AAND,0BAMC;AAED,SAAgB,WAAW,CAAC,WAAmB;IAC7C,OAAO,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAChG,CAAC;AAFD,kCAEC;AAED,SAAgB,UAAU,CAAC,WAAmB;IAC5C,OAAO,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/F,CAAC;AAFD,gCAEC", "sourcesContent": ["export type Orientation =\n  | 'any'\n  | 'natural'\n  | 'landscape'\n  | 'landscape-primary'\n  | 'landscape-secondary'\n  | 'portrait'\n  | 'portrait-primary'\n  | 'portrait-secondary'\n  | 'omit';\n\n// https://developer.mozilla.org/en-US/docs/Web/Manifest#orientation\nconst ANY_ORIENTATIONS = ['any', 'natural', 'omit'];\n\nconst PORTRAIT_ORIENTATIONS = ['portrait', 'portrait-primary', 'portrait-secondary'];\n\nconst LANDSCAPE_ORIENTATIONS = ['landscape', 'landscape-primary', 'landscape-secondary'];\n\nexport function isValid(orientation: string): orientation is Orientation {\n  return (\n    ANY_ORIENTATIONS.includes(orientation) ||\n    LANDSCAPE_ORIENTATIONS.includes(orientation) ||\n    PORTRAIT_ORIENTATIONS.includes(orientation)\n  );\n}\n\nexport function isLandscape(orientation: string): boolean {\n  return ANY_ORIENTATIONS.includes(orientation) || LANDSCAPE_ORIENTATIONS.includes(orientation);\n}\n\nexport function isPortrait(orientation: string): boolean {\n  return ANY_ORIENTATIONS.includes(orientation) || PORTRAIT_ORIENTATIONS.includes(orientation);\n}\n"]}