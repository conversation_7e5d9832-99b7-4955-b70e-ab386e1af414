{"version": 3, "file": "update.js", "sourceRoot": "", "sources": ["../src/update.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,gEAA0C;AAE3B,KAAK,UAAU,YAAY;IACxC,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAErD,MAAM,MAAM,GAAG,IAAA,sBAAc,EAAC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IAE/D,IAAI;QACF,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC;QACzB,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;YACrB,MAAM,YAAY,GAAG,WAAW,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,YAAY,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC;YACzF,OAAO,CAAC,GAAG,CAAC,6BAA6B,GAAG,eAAK,CAAC,IAAI,CAAC,YAAY,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzF,OAAO,CAAC,GAAG,EAAE,CAAC;SACf;KACF;IAAC,MAAM;QACN,eAAe;KAChB;AACH,CAAC;AAjBD,+BAiBC", "sourcesContent": ["import chalk from 'chalk';\nimport checkForUpdate from 'update-check';\n\nexport default async function shouldUpdate() {\n  const packageJson = () => require('../package.json');\n\n  const update = checkForUpdate(packageJson()).catch(() => null);\n\n  try {\n    const res = await update;\n    if (res && res.latest) {\n      const _packageJson = packageJson();\n      console.log();\n      console.log(chalk.yellow.bold(`A new version of \\`${_packageJson.name}\\` is available`));\n      console.log('You can update by running: ' + chalk.cyan(`npm i -g ${_packageJson.name}`));\n      console.log();\n    }\n  } catch {\n    // ignore error\n  }\n}\n"]}