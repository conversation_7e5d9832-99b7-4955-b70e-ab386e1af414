{"version": 3, "file": "Splash.js", "sourceRoot": "", "sources": ["../src/Splash.ts"], "names": [], "mappings": ";;;AAAA,+CAA8E;AAU9E,MAAM,OAAO,GAAa;IACxB,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;IAC7E,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;IAClF,EAAE,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;IAC7E,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;IAC1F;QACE,KAAK,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC;QAC5E,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,KAAK;KAChB;IACD;QACE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;QACxD,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,KAAK;KAChB;IACD,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAClF,EAAE,KAAK,EAAE,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAChF,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAClF,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC1F,CAAC;AAEF,SAAgB,wBAAwB,CACtC,KAAa,EACb,MAAc,EACd,KAAa,EACb,WAAmB;IAEnB,MAAM,MAAM,GAAG;QACb,cAAc,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI;QAChD,eAAe,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI;QAClD,4BAA4B,EAAE,KAAK;QACnC,WAAW;KACZ,CAAC;IAEF,aAAa;IACb,MAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAExF,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAjBD,4DAiBC;AAED,SAAgB,UAAU,CAAC;AACzB,oCAAoC;AACpC,WAAW,GAAG,UAAU,EACxB,cAAc,GAAG,IAAI,MAInB,EAAE;IACJ,IAAI,CAAC,IAAA,qBAAO,EAAC,WAAW,CAAC,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,GAAG,WAAW,6BAA6B,CAAC,CAAC;KAC9D;IAED,MAAM,YAAY,GAAkB,EAAE,CAAC;IACvC,IAAI,IAAA,yBAAW,EAAC,WAAW,CAAC,EAAE;QAC5B,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAChC;IACD,IAAI,IAAA,wBAAU,EAAC,WAAW,CAAC,EAAE;QAC3B,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC/B;IAED,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,cAAc,EAAE;QAClB,OAAO,GAAG,OAAO,CAAC;KACnB;SAAM;QACL,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;KACvD;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AA5BD,gCA4BC", "sourcesContent": ["import { isLandscape, isPortrait, isValid, Orientation } from './Orientation';\n\ninterface Device {\n  names: string[];\n  width: number;\n  height: number;\n  scale: number;\n  isTablet: boolean;\n}\n\nconst Devices: Device[] = [\n  { names: ['iPhone SE'], width: 640, height: 1136, scale: 2, isTablet: false },\n  { names: ['iPhone Xs Max'], width: 1242, height: 2688, scale: 3, isTablet: false },\n  { names: ['iPhone Xr'], width: 828, height: 1792, scale: 2, isTablet: false },\n  { names: ['iPhone X', 'iPhone Xs'], width: 1125, height: 2436, scale: 3, isTablet: false },\n  {\n    names: ['iPhone 8 Plus', 'iPhone 7 Plus', 'iPhone 6s Plus', 'iPhone 6 Plus'],\n    width: 1242,\n    height: 2208,\n    scale: 3,\n    isTablet: false,\n  },\n  {\n    names: ['iPhone 8', 'iPhone 7', 'iPhone 6s', 'iPhone 6'],\n    width: 750,\n    height: 1334,\n    scale: 2,\n    isTablet: false,\n  },\n  { names: ['iPad Pro 12.9\"'], width: 2048, height: 2732, scale: 2, isTablet: true },\n  { names: ['iPad Pro 11\"'], width: 1668, height: 2388, scale: 2, isTablet: true },\n  { names: ['iPad Pro 10.5\"'], width: 1668, height: 2224, scale: 2, isTablet: true },\n  { names: ['iPad Mini', 'iPad Air'], width: 1536, height: 2048, scale: 2, isTablet: true },\n];\n\nexport function assembleOrientationMedia(\n  width: number,\n  height: number,\n  scale: number,\n  orientation: string\n): string {\n  const params = {\n    'device-width': `${Math.floor(width / scale)}px`,\n    'device-height': `${Math.floor(height / scale)}px`,\n    '-webkit-device-pixel-ratio': scale,\n    orientation,\n  };\n\n  // @ts-ignore\n  const query = ['screen', ...Object.keys(params).map(key => `(${key}: ${params[key]})`)];\n\n  return query.join(' and ');\n}\n\nexport function getDevices({\n  // disable landscape PWAs by default\n  orientation = 'portrait',\n  supportsTablet = true,\n}: {\n  orientation?: Orientation;\n  supportsTablet?: boolean;\n} = {}): (Device & { orientations: Orientation[] })[] {\n  if (!isValid(orientation)) {\n    throw new Error(`${orientation} is not a valid orientation`);\n  }\n\n  const orientations: Orientation[] = [];\n  if (isLandscape(orientation)) {\n    orientations.push('landscape');\n  }\n  if (isPortrait(orientation)) {\n    orientations.push('portrait');\n  }\n\n  let devices = [];\n  if (supportsTablet) {\n    devices = Devices;\n  } else {\n    devices = Devices.filter(({ isTablet }) => !isTablet);\n  }\n\n  return devices.map(device => ({ ...device, orientations }));\n}\n"]}