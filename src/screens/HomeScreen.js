import React from 'react';
import { View, Text, TouchableOpacity, Image, SafeAreaView, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles/colors';

export default function HomeScreen({ navigation }) {
  const handleTakePhoto = () => {
    console.log('Take Photo button pressed!');
    navigation.navigate('Camera', { mode: 'camera' });
  };

  const handleUploadPhoto = () => {
    console.log('Upload Photo button pressed!');
    navigation.navigate('Camera', { mode: 'library' });
  };

  const testTouch = () => {
    console.log('TEST BUTTON WORKS!');
    alert('Touch events are working!');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.logoPlaceholder}>
            <Ionicons name="leaf" size={48} color={colors.primary} />
          </View>
          <Text style={styles.title}>Garden Guru</Text>
          <Text style={styles.subtitle}>
            Your personal gardening assistant
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleTakePhoto}
            activeOpacity={0.7}
          >
            <View style={styles.buttonIcon}>
              <Ionicons name="camera" size={28} color={colors.primary} />
            </View>
            <View style={styles.buttonTextContainer}>
              <Text style={styles.buttonTitle}>Take a Photo</Text>
              <Text style={styles.buttonSubtitle}>Identify a plant using your camera</Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleUploadPhoto}
            activeOpacity={0.7}
          >
            <View style={styles.buttonIcon}>
              <Ionicons name="images" size={28} color={colors.primary} />
            </View>
            <View style={styles.buttonTextContainer}>
              <Text style={styles.buttonTitle}>Upload a Photo</Text>
              <Text style={styles.buttonSubtitle}>Choose a plant image from your gallery</Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="white" />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.primaryButton, { backgroundColor: 'red', marginBottom: 16 }]}
          onPress={testTouch}
        >
          <Text style={styles.buttonTitle}>TEST BUTTON - TAP ME</Text>
        </TouchableOpacity>

        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>Quick Tips</Text>
          <Text style={styles.tipsText}>
            • Take clear, well-lit photos of plants{'\n'}
            • Focus on leaves and flowers for better identification{'\n'}
            • Check your plant library for saved plants
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    marginTop: 16,
  },
  logoPlaceholder: {
    width: 96,
    height: 96,
    marginBottom: 8,
    backgroundColor: colors.surface,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  subtitle: {
    color: colors.textLight,
    textAlign: 'center',
    marginTop: 8,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonIcon: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
    marginRight: 16,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonTitle: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonSubtitle: {
    color: colors.white,
    opacity: 0.8,
  },
  tipsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
  },
  tipsTitle: {
    color: colors.text,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 8,
  },
  tipsText: {
    color: colors.textLight,
  },
});
