import React from 'react';
import { View, Text, TouchableOpacity, Pressable, Image, SafeAreaView, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles/colors';

export default function HomeScreen({ navigation }) {
  const [touchCount, setTouchCount] = React.useState(0);

  const handleTakePhoto = () => {
    console.log('Take Photo button pressed!');
    navigation.navigate('Camera', { mode: 'camera' });
  };

  const handleUploadPhoto = () => {
    console.log('Upload Photo button pressed!');
    navigation.navigate('Camera', { mode: 'library' });
  };

  const testTouch = () => {
    console.log('TEST BUTTON WORKS!');
    alert('Touch events are working!');
  };

  const handleTouchStart = () => {
    console.log('Touch start detected!');
    setTouchCount(prev => prev + 1);
  };

  return (
    <View
      style={{ flex: 1, backgroundColor: 'white', justifyContent: 'center', alignItems: 'center' }}
      onTouchStart={handleTouchStart}
    >
      <Text style={{ fontSize: 32, marginBottom: 20, color: 'black' }}>
        TOUCH TEST - Touches: {touchCount}
      </Text>

      <Text style={{ fontSize: 18, marginBottom: 20, color: 'red', textAlign: 'center' }}>
        TAP ANYWHERE ON SCREEN{'\n'}
        Counter should increase if touch works
      </Text>

      <Pressable
        style={{ backgroundColor: 'blue', padding: 20, marginBottom: 10 }}
        onPress={() => {
          console.log('BLUE PRESSED');
          alert('BLUE BUTTON WORKS!');
        }}
      >
        <Text style={{ color: 'white', fontSize: 18 }}>TAP BLUE BUTTON</Text>
      </Pressable>

      <TouchableOpacity
        style={{ backgroundColor: 'green', padding: 20, marginBottom: 10 }}
        onPress={() => {
          console.log('GREEN PRESSED');
          alert('GREEN BUTTON WORKS!');
        }}
      >
        <Text style={{ color: 'white', fontSize: 18 }}>TAP GREEN BUTTON</Text>
      </TouchableOpacity>

      <View
        style={{ backgroundColor: 'red', padding: 20 }}
        onTouchStart={() => {
          console.log('RED TOUCHED');
          alert('RED VIEW WORKS!');
        }}
      >
        <Text style={{ color: 'white', fontSize: 18 }}>TAP RED VIEW</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    marginTop: 16,
  },
  logoPlaceholder: {
    width: 96,
    height: 96,
    marginBottom: 8,
    backgroundColor: colors.surface,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  subtitle: {
    color: colors.textLight,
    textAlign: 'center',
    marginTop: 8,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 80,
    overflow: 'visible',
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 80,
    overflow: 'visible',
  },
  buttonIcon: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
    marginRight: 16,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonTitle: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonSubtitle: {
    color: colors.white,
    opacity: 0.8,
  },
  tipsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
  },
  tipsTitle: {
    color: colors.text,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 8,
  },
  tipsText: {
    color: colors.textLight,
  },
});
