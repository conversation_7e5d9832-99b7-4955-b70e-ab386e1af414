import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Image, ActivityIndicator, Alert, StyleSheet } from 'react-native';
import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles/colors';

export default function CameraScreen({ navigation, route }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(Camera.Constants.Type.back);
  const [loading, setLoading] = useState(false);
  const cameraRef = useRef(null);
  const mode = route.params?.mode || 'camera';

  useEffect(() => {
    (async () => {
      if (mode === 'camera') {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');

        if (status !== 'granted') {
          Alert.alert(
            "Permission Required",
            "Camera access is needed to take plant photos",
            [{ text: "OK", onPress: () => navigation.goBack() }]
          );
        }
      } else if (mode === 'library') {
        pickImage();
      }
    })();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current) {
      setLoading(true);
      try {
        const photo = await cameraRef.current.takePictureAsync({ quality: 0.7 });
        const resizedPhoto = await ImageManipulator.manipulateAsync(
          photo.uri,
          [{ resize: { width: 800 } }],
          { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
        );

        // In a real app, you would upload this image to a plant identification API
        // For now, we'll simulate the API call with a timeout
        setTimeout(() => {
          setLoading(false);
          navigation.navigate('PlantDetails', {
            imageUri: resizedPhoto.uri,
            // Simulated plant data
            plantData: {
              name: "Monstera Deliciosa",
              scientificName: "Monstera deliciosa",
              care: {
                light: "Bright indirect light",
                water: "Allow soil to dry between waterings",
                humidity: "Prefers high humidity",
                temperature: "65-85°F (18-29°C)"
              }
            }
          });
        }, 2000);
      } catch (error) {
        console.log(error);
        setLoading(false);
        Alert.alert("Error", "Failed to take picture");
      }
    }
  };

  const pickImage = async () => {
    setLoading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.7,
      });

      if (!result.canceled) {
        const resizedPhoto = await ImageManipulator.manipulateAsync(
          result.assets[0].uri,
          [{ resize: { width: 800 } }],
          { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
        );

        // Simulate API call
        setTimeout(() => {
          setLoading(false);
          navigation.navigate('PlantDetails', {
            imageUri: resizedPhoto.uri,
            // Simulated plant data
            plantData: {
              name: "Fiddle Leaf Fig",
              scientificName: "Ficus lyrata",
              care: {
                light: "Bright indirect light",
                water: "Water when top inch of soil is dry",
                humidity: "Average to high humidity",
                temperature: "60-75°F (15-24°C)"
              }
            }
          });
        }, 2000);
      } else {
        setLoading(false);
        navigation.goBack();
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
      Alert.alert("Error", "Failed to select image");
      navigation.goBack();
    }
  };

  if (hasPermission === null && mode === 'camera') {
    return <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary} />
    </View>;
  }

  if (hasPermission === false && mode === 'camera') {
    return <View style={styles.permissionContainer}>
      <Text style={styles.errorText}>No access to camera</Text>
      <TouchableOpacity
        style={styles.goBackButton}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.goBackButtonText}>Go Back</Text>
      </TouchableOpacity>
    </View>;
  }

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Identifying plant...</Text>
      </View>
    );
  }

  if (mode === 'library') {
    return null; // This screen will navigate away after picking an image
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={cameraRef}
        type={type}
        style={styles.camera}
        ratio="4:3"
      >
        <View style={styles.cameraOverlay}>
          <View style={styles.cameraControls}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="close" size={28} color="white" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.captureButton}
              onPress={takePicture}
            >
              <View style={styles.captureButtonInner} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={() => {
                setType(
                  type === Camera.Constants.Type.back
                    ? Camera.Constants.Type.front
                    : Camera.Constants.Type.back
                );
              }}
            >
              <Ionicons name="camera-reverse" size={28} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </Camera>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    flexDirection: 'row',
  },
  cameraControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    paddingBottom: 32,
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 8,
    borderRadius: 20,
  },
  captureButton: {
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 40,
  },
  captureButtonInner: {
    backgroundColor: colors.white,
    borderWidth: 2,
    borderColor: colors.black,
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 18,
    textAlign: 'center',
  },
  goBackButton: {
    marginTop: 16,
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  goBackButtonText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  loadingText: {
    marginTop: 16,
    color: colors.primary,
    fontWeight: 'bold',
  },
});
