import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles/colors';

// Soil mix materials
const soilMaterials = [
  { id: 1, name: 'Potting Soil', icon: 'leaf' },
  { id: 2, name: 'Perlite', icon: 'water' },
  { id: 3, name: 'Coconut Coir', icon: 'nutrition' },
  { id: 4, name: 'Compost', icon: 'leaf' },
  { id: 5, name: 'Worm Castings', icon: 'nutrition' },
  { id: 6, name: 'Sand', icon: 'water' },
  { id: 7, name: 'Bark Chips', icon: 'leaf' },
];

export default function PlantDetailsScreen({ route, navigation }) {
  const { imageUri, plantData } = route.params;
  const [notes, setNotes] = useState('');

  // Generate soil mix recommendation based on plant type
  // In a real app, this would use more sophisticated logic
  const getSoilMixRecommendation = () => {
    // Simple recommendation based on plant name
    if (plantData.name.toLowerCase().includes('monstera')) {
      return [
        { material: 'Potting Soil', percentage: 60 },
        { material: 'Perlite', percentage: 20 },
        { material: 'Coconut Coir', percentage: 10 },
        { material: 'Bark Chips', percentage: 10 },
      ];
    } else if (plantData.name.toLowerCase().includes('fig')) {
      return [
        { material: 'Potting Soil', percentage: 50 },
        { material: 'Perlite', percentage: 20 },
        { material: 'Compost', percentage: 20 },
        { material: 'Worm Castings', percentage: 10 },
      ];
    } else {
      // Default mix
      return [
        { material: 'Potting Soil', percentage: 70 },
        { material: 'Perlite', percentage: 20 },
        { material: 'Compost', percentage: 10 },
      ];
    }
  };

  const soilMix = getSoilMixRecommendation();

  const handleSaveProfile = () => {
    navigation.navigate('PlantProfile', {
      imageUri,
      plantData,
      soilMix,
      notes,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View className="bg-surface rounded-xl overflow-hidden mb-6">
            <Image
              source={{ uri: imageUri }}
              className="w-full h-64"
              resizeMode="cover"
            />
            <View className="p-4">
              <Text className="text-2xl font-bold text-primary">{plantData.name}</Text>
              <Text className="text-textLight italic">{plantData.scientificName}</Text>
            </View>
          </View>

          {/* Care Guide */}
          <View className="bg-surface rounded-xl p-4 mb-6">
            <Text className="text-xl font-bold text-primary mb-4">Care Guide</Text>

            <View className="flex-row items-center mb-3">
              <View className="bg-secondary/20 p-2 rounded-full mr-3">
                <Ionicons name="sunny" size={24} color="#2E7D32" />
              </View>
              <View className="flex-1">
                <Text className="font-bold text-text">Light</Text>
                <Text className="text-textLight">{plantData.care.light}</Text>
              </View>
            </View>

            <View className="flex-row items-center mb-3">
              <View className="bg-secondary/20 p-2 rounded-full mr-3">
                <Ionicons name="water" size={24} color="#2E7D32" />
              </View>
              <View className="flex-1">
                <Text className="font-bold text-text">Water</Text>
                <Text className="text-textLight">{plantData.care.water}</Text>
              </View>
            </View>

            <View className="flex-row items-center mb-3">
              <View className="bg-secondary/20 p-2 rounded-full mr-3">
                <Ionicons name="thermometer" size={24} color="#2E7D32" />
              </View>
              <View className="flex-1">
                <Text className="font-bold text-text">Temperature</Text>
                <Text className="text-textLight">{plantData.care.temperature}</Text>
              </View>
            </View>

            <View className="flex-row items-center">
              <View className="bg-secondary/20 p-2 rounded-full mr-3">
                <Ionicons name="water-outline" size={24} color="#2E7D32" />
              </View>
              <View className="flex-1">
                <Text className="font-bold text-text">Humidity</Text>
                <Text className="text-textLight">{plantData.care.humidity}</Text>
              </View>
            </View>
          </View>

          {/* Soil Mix */}
          <View className="bg-surface rounded-xl p-4 mb-6">
            <Text className="text-xl font-bold text-primary mb-4">Recommended Soil Mix</Text>

            {soilMix.map((item, index) => (
              <View key={index} className="flex-row items-center mb-3">
                <View
                  className="w-10 h-10 rounded-full bg-secondary/20 items-center justify-center mr-3"
                  style={{ opacity: item.percentage / 100 + 0.3 }}
                >
                  <Text className="font-bold text-primary">{item.percentage}%</Text>
                </View>
                <Text className="text-text flex-1">{item.material}</Text>
              </View>
            ))}

            <Text className="text-textLight mt-2">
              Mix these ingredients thoroughly for optimal growth.
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSaveProfile}
        >
          <Text style={styles.saveButtonText}>Generate Plant Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.gray,
  },
  saveButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 18,
  },
});
